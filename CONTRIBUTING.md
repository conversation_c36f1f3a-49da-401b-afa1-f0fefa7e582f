# Contributing to LLM Memory Test

Thank you for your interest in contributing to the LLM Memory Test framework! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Process](#contributing-process)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Documentation](#documentation)
- [Submitting Changes](#submitting-changes)

## Code of Conduct

This project adheres to a Code of Conduct that all contributors are expected to follow. Please read [CODE_OF_CONDUCT.md](CODE_OF_CONDUCT.md) before contributing.

## Getting Started

### Prerequisites

- Node.js 18.0.0 or higher
- npm or yarn package manager
- Git

### Development Setup

1. Fork the repository on GitHub
2. Clone your fork locally:
   ```bash
   git clone https://github.com/your-username/llm-memory-test.git
   cd llm-memory-test
   ```

3. Install dependencies:
   ```bash
   npm install
   ```

4. Copy the environment configuration:
   ```bash
   cp .env.example .env
   ```

5. Configure your environment variables in `.env`

6. Run the test suite to ensure everything is working:
   ```bash
   npm test
   ```

7. Run the health check:
   ```bash
   npm run health-check
   ```

## Contributing Process

### 1. Choose an Issue

- Look for issues labeled `good first issue` for newcomers
- Check existing issues and discussions before creating new ones
- Comment on issues you'd like to work on to avoid duplication

### 2. Create a Branch

Create a feature branch from `main`:
```bash
git checkout -b feature/your-feature-name
```

Use descriptive branch names:
- `feature/add-new-memory-type`
- `fix/api-retry-logic`
- `docs/improve-readme`

### 3. Make Changes

- Follow the coding standards outlined below
- Write tests for new functionality
- Update documentation as needed
- Ensure all tests pass

## Coding Standards

### JavaScript Style

We use ESLint and Prettier for code formatting:

```bash
# Check linting
npm run lint

# Fix linting issues
npm run lint:fix

# Format code
npm run format

# Check formatting
npm run format:check
```

### Code Quality Guidelines

1. **Functions**: Keep functions small and focused on a single responsibility
2. **Documentation**: Add JSDoc comments for all public methods and classes
3. **Error Handling**: Use proper error handling with descriptive messages
4. **Naming**: Use descriptive variable and function names
5. **Constants**: Use UPPER_CASE for constants
6. **Async/Await**: Prefer async/await over promises for better readability

### JSDoc Documentation

All public methods should include JSDoc comments:

```javascript
/**
 * Evaluates memory performance based on conversation context
 * @param {Array} conversationLog - Array of conversation messages
 * @param {Array} testFacts - Array of test facts to evaluate
 * @param {Object} memoryContext - Current memory context
 * @returns {Promise<Object>} Evaluation results with scores and analysis
 * @throws {Error} When evaluation fails due to invalid input
 */
async function evaluateMemory(conversationLog, testFacts, memoryContext) {
  // Implementation
}
```

## Testing Guidelines

### Test Structure

- Unit tests: `src/**/__tests__/*.test.js`
- Integration tests: `src/__tests__/integration/*.test.js`
- Test utilities: `src/__tests__/utils/`

### Writing Tests

1. **Test Coverage**: Aim for high test coverage, especially for core functionality
2. **Test Names**: Use descriptive test names that explain what is being tested
3. **Mocking**: Use mocks for external dependencies (APIs, file system)
4. **Assertions**: Use clear, specific assertions

Example test structure:
```javascript
describe('MemoryFactory', () => {
  describe('createMemory', () => {
    it('should create SimpleMemory when type is "simple"', () => {
      // Test implementation
    });

    it('should throw error for unsupported memory type', () => {
      // Test implementation
    });
  });
});
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Documentation

### README Updates

When adding new features, update the README.md with:
- Usage examples
- Configuration options
- API documentation

### Code Comments

- Add inline comments for complex logic
- Explain the "why" not just the "what"
- Keep comments up to date with code changes

### Architecture Documentation

For significant changes, update:
- `ARCHITECTURE.md` - System architecture and design decisions
- `CONFIGURATION.md` - Configuration options and examples

## Submitting Changes

### Before Submitting

Run the pre-commit checks:
```bash
npm run precommit
```

This will:
- Run linting
- Check code formatting
- Run the test suite

### Pull Request Process

1. **Update Documentation**: Ensure all documentation is updated
2. **Add Tests**: Include tests for new functionality
3. **Update CHANGELOG**: Add entry to CHANGELOG.md
4. **Commit Messages**: Use clear, descriptive commit messages

### Commit Message Format

Use conventional commit format:
```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

Examples:
```
feat(memory): add Redis-based memory implementation
fix(api): handle rate limiting with exponential backoff
docs(readme): add installation troubleshooting section
```

### Pull Request Template

When creating a pull request, include:

1. **Description**: Clear description of changes
2. **Motivation**: Why the change is needed
3. **Testing**: How the change was tested
4. **Breaking Changes**: Any breaking changes
5. **Checklist**: Completed checklist items

## Types of Contributions

### Bug Reports

When reporting bugs, include:
- Clear description of the issue
- Steps to reproduce
- Expected vs actual behavior
- Environment details (Node.js version, OS)
- Relevant logs or error messages

### Feature Requests

For feature requests, provide:
- Clear description of the feature
- Use case and motivation
- Proposed implementation approach
- Examples of similar features in other projects

### Code Contributions

We welcome contributions for:
- New memory implementations
- Performance improvements
- Bug fixes
- Documentation improvements
- Test coverage improvements
- Tooling and development experience

## Getting Help

- **Issues**: Create an issue for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions and ideas
- **Documentation**: Check existing documentation first

## Recognition

Contributors will be recognized in:
- CHANGELOG.md for their contributions
- README.md contributors section
- GitHub contributors page

Thank you for contributing to LLM Memory Test!