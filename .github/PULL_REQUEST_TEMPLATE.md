# Pull Request

## Description

Please provide a clear and concise description of the changes in this PR.

## Type of Change

Please delete options that are not relevant:

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Test improvements
- [ ] Other (please describe): ___________

## Related Issues

Please link any related issues:
- Fixes #(issue number)
- Closes #(issue number)
- Related to #(issue number)

## Changes Made

Please provide a detailed list of changes:

- [ ] Added/modified functionality X
- [ ] Updated documentation for Y
- [ ] Fixed bug in Z
- [ ] Added tests for A

## Testing

Please describe the tests that you ran to verify your changes:

- [ ] Unit tests pass (`npm test`)
- [ ] Integration tests pass
- [ ] Linting passes (`npm run lint`)
- [ ] Code formatting is correct (`npm run format:check`)
- [ ] Manual testing performed

### Test Configuration

If applicable, describe your test configuration:
- Node.js version:
- Operating system:
- Test data used:

## Breaking Changes

If this PR introduces breaking changes, please describe them and provide migration instructions:

## Documentation

- [ ] I have updated the README.md if needed
- [ ] I have updated the CHANGELOG.md
- [ ] I have added/updated JSDoc comments
- [ ] I have updated configuration documentation if needed

## Screenshots (if applicable)

Please add screenshots to help explain your changes.

## Performance Impact

If applicable, describe the performance impact of your changes:

- [ ] No performance impact
- [ ] Performance improvement (please describe)
- [ ] Potential performance regression (please describe and justify)

## Checklist

Please check all that apply:

- [ ] My code follows the project's coding standards
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published

## Additional Notes

Please add any additional notes, concerns, or questions here.