---
name: Bug report
about: Create a report to help us improve
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Set up environment with '...'
2. Run command '....'
3. Configure memory type as '....'
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen.

**Actual behavior**
A clear and concise description of what actually happened.

**Error messages**
If applicable, add error messages or logs to help explain your problem.

```
Paste error messages here
```

**Environment (please complete the following information):**
- OS: [e.g. macOS, Ubuntu 20.04, Windows 10]
- Node.js version: [e.g. 18.17.0]
- npm version: [e.g. 9.6.7]
- Package version: [e.g. 1.0.0]

**Configuration**
Please share your configuration (remove sensitive information like API keys):

```json
{
  "memoryType": "...",
  "models": {
    "user": "...",
    "assistant": "..."
  }
}
```

**Test data**
If applicable, describe the test data you were using:
- Test facts file: [e.g. test_facts_simple.json]
- Number of facts: [e.g. 10]
- Messages between facts: [e.g. 3]

**Additional context**
Add any other context about the problem here.

**Possible solution**
If you have ideas about what might be causing the issue or how to fix it, please share them here.

**Checklist**
- [ ] I have searched existing issues to make sure this is not a duplicate
- [ ] I have included all relevant information above
- [ ] I have removed any sensitive information (API keys, personal data)
- [ ] I can reproduce this issue consistently