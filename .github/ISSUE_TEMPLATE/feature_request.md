---
name: Feature request
about: Suggest an idea for this project
title: '[FEATURE] '
labels: 'enhancement'
assignees: ''

---

**Is your feature request related to a problem? Please describe.**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Use case**
Describe the specific use case or scenario where this feature would be helpful.

**Implementation ideas**
If you have ideas about how this could be implemented, please share them here.

**Examples**
If there are similar features in other projects or tools, please provide examples or links.

**Additional context**
Add any other context, screenshots, or examples about the feature request here.

**Impact**
- Who would benefit from this feature?
- How would it improve the project?
- Is this a breaking change?

**Checklist**
- [ ] I have searched existing issues to make sure this is not a duplicate
- [ ] I have provided a clear description of the feature
- [ ] I have explained the use case and benefits
- [ ] I have considered the impact on existing functionality