---
name: Documentation improvement
about: Suggest improvements to documentation
title: '[DOCS] '
labels: 'documentation'
assignees: ''

---

**What documentation needs improvement?**
Please specify which documentation needs to be improved:
- [ ] README.md
- [ ] API documentation
- [ ] Code comments
- [ ] Architecture documentation
- [ ] Configuration guide
- [ ] Contributing guidelines
- [ ] Other: ___________

**Current issue**
Describe what's unclear, missing, or incorrect in the current documentation.

**Suggested improvement**
Describe what should be added, changed, or clarified.

**Target audience**
Who would benefit from this documentation improvement?
- [ ] New users getting started
- [ ] Developers extending the framework
- [ ] Contributors to the project
- [ ] Advanced users
- [ ] Other: ___________

**Examples**
If you have examples of good documentation from other projects, please share links.

**Additional context**
Add any other context about the documentation improvement here.

**Checklist**
- [ ] I have checked that this documentation issue doesn't already exist
- [ ] I have clearly described what needs to be improved
- [ ] I have explained who would benefit from this improvement