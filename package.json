{"name": "llm-memory-test", "version": "1.0.0", "description": "A comprehensive framework for testing and evaluating different memory implementations in conversational AI systems", "main": "src/app.js", "bin": {"llm-memory-test": "./src/app.js"}, "exports": {".": "./src/app.js", "./memory": "./src/memory/index.js", "./utils": "./src/utils/index.js", "./config": "./src/config/index.js"}, "files": ["src/", "data/", "README.md", "LICENSE", "CHANGELOG.md"], "homepage": "https://github.com/your-username/llm-memory-test#readme", "repository": {"type": "git", "url": "git+https://github.com/your-username/llm-memory-test.git"}, "bugs": {"url": "https://github.com/your-username/llm-memory-test/issues"}, "scripts": {"start": "node src/app.js", "dev": "node --watch src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/ --ext .js || echo 'No files to lint'", "lint:fix": "eslint src/ --ext .js --fix || echo 'No files to lint'", "format": "prettier --write \"src/**/*.js\" \"*.{js,json,md}\"", "format:check": "prettier --check \"src/**/*.js\" \"*.{js,json,md}\"", "precommit": "npm run lint && npm run format:check && npm run test", "prepare": "npm run lint && npm run test", "prepublishOnly": "npm run test && npm run lint", "version": "npm run format && git add -A src", "postversion": "git push && git push --tags", "verify": "node scripts/verify-installation.js", "health-check": "node scripts/health-check.js", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish"}, "keywords": ["llm", "memory", "test", "ai", "conversation", "evaluation", "chatbot", "machine-learning", "natural-language-processing", "conversational-ai", "memory-testing", "llm-evaluation", "ai-framework", "testing-framework"], "author": "LLM Memory Test Contributors", "license": "MIT", "dependencies": {"@langchain/openai": "^0.5.5", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "dotenv": "^16.4.7", "langchain": "^0.3.21", "uuid": "^11.1.0"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0"}}