/**
 * Report Generator - Advanced Result Reporting and Visualization System
 *
 * This module provides comprehensive reporting capabilities for LLM memory test results,
 * including detailed performance reports, comparative analysis reports, and multiple
 * export formats (JSON, CSV, HTML). It also includes summary dashboards for test
 * execution monitoring.
 *
 * Features:
 * - Detailed performance reports with charts and graphs
 * - Comparative analysis reports for different memory implementations
 * - Multiple export formats (JSON, CSV, HTML)
 * - Summary dashboards for test execution monitoring
 * - Interactive HTML reports with visualizations
 * - Trend analysis and regression detection reports
 * - Performance metrics and statistical analysis integration
 *
 * @module ReportGenerator
 * 
 * @example
 * const generator = new ReportGenerator();
 * const htmlReport = await generator.generateHTMLReport(testResults, analysis);
 * await generator.exportToFile(htmlReport, 'report.html', 'html');
 */

const fs = require('fs').promises;
const path = require('path');
const { StatisticalAnalyzer } = require('./statisticalAnalysis');
const { defaultLogger } = require('./logger');

/**
 * Report Generator Class
 * 
 * Provides comprehensive reporting and visualization capabilities for test results
 * including multiple export formats and interactive dashboards.
 */
class ReportGenerator {
  /**
   * Creates a new ReportGenerator instance
   * 
   * @param {Object} [options] - Configuration options
   * @param {string} [options.outputDirectory='./reports'] - Default output directory for reports
   * @param {string} [options.templateDirectory='./templates'] - Directory containing report templates
   * @param {boolean} [options.includeCharts=true] - Whether to include charts in HTML reports
   * @param {boolean} [options.enableInteractivity=true] - Enable interactive features in HTML reports
   * @param {string} [options.theme='default'] - Report theme (default, dark, light)
   */
  constructor(options = {}) {
    this.options = {
      outputDirectory: './reports',
      templateDirectory: './templates',
      includeCharts: true,
      enableInteractivity: true,
      theme: 'default',
      ...options
    };

    this.statisticalAnalyzer = new StatisticalAnalyzer();

    defaultLogger.info('ReportGenerator initialized', {
      options: this.options
    });
  }

  /**
   * Generate comprehensive HTML report with visualizations
   * 
   * @param {Array} testResults - Array of test result objects
   * @param {Object} [analysis] - Pre-computed statistical analysis
   * @param {Object} [options] - Report generation options
   * @returns {string} HTML report content
   */
  async generateHTMLReport(testResults, analysis = null, options = {}) {
    try {
      // Perform statistical analysis if not provided
      if (!analysis) {
        analysis = await this.statisticalAnalyzer.analyzeTestResults(testResults);
      }

      const reportOptions = { ...this.options, ...options };
      
      const htmlContent = this._generateHTMLContent(testResults, analysis, reportOptions);
      
      defaultLogger.info('HTML report generated successfully', {
        testResultsCount: testResults.length,
        reportSize: htmlContent.length
      });

      return htmlContent;
    } catch (error) {
      defaultLogger.error('Failed to generate HTML report', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate comparative analysis report for different memory implementations
   * 
   * @param {Array} testResults - Array of test result objects
   * @param {Object} [options] - Report generation options
   * @returns {Object} Comparative analysis report
   */
  async generateComparativeReport(testResults, options = {}) {
    try {
      const analysis = await this.statisticalAnalyzer.analyzeTestResults(testResults);
      
      if (analysis.comparative.insufficient_data) {
        throw new Error('Insufficient data for comparative analysis');
      }

      const report = {
        metadata: {
          generatedAt: new Date().toISOString(),
          testResultsCount: testResults.length,
          memoryTypes: analysis.comparative.memoryTypes,
          reportType: 'comparative'
        },
        executive_summary: this._generateExecutiveSummary(analysis),
        performance_rankings: this._generatePerformanceRankings(analysis.comparative),
        statistical_significance: this._generateSignificanceReport(analysis.comparative),
        recommendations: this._generateComparativeRecommendations(analysis),
        detailed_analysis: analysis.comparative,
        charts: this._generateChartData(testResults, analysis)
      };

      defaultLogger.info('Comparative report generated successfully', {
        memoryTypes: analysis.comparative.memoryTypes.length,
        reportSections: Object.keys(report).length
      });

      return report;
    } catch (error) {
      defaultLogger.error('Failed to generate comparative report', { error: error.message });
      throw error;
    }
  }

  /**
   * Generate performance dashboard data
   * 
   * @param {Array} testResults - Array of test result objects
   * @param {Object} [options] - Dashboard generation options
   * @returns {Object} Dashboard data structure
   */
  async generateDashboard(testResults, options = {}) {
    try {
      const analysis = await this.statisticalAnalyzer.analyzeTestResults(testResults);
      
      const dashboard = {
        metadata: {
          generatedAt: new Date().toISOString(),
          testResultsCount: testResults.length,
          timeRange: this._calculateTimeRange(testResults),
          refreshInterval: options.refreshInterval || 300000 // 5 minutes
        },
        summary_metrics: this._generateSummaryMetrics(analysis),
        performance_trends: this._generateTrendData(analysis.trends),
        regression_alerts: this._generateRegressionAlerts(analysis.regression),
        recent_tests: this._generateRecentTestsData(testResults),
        memory_comparison: this._generateMemoryComparisonData(analysis.comparative),
        system_health: this._generateSystemHealthData(testResults),
        charts: {
          performance_over_time: this._generatePerformanceTimeChart(testResults),
          memory_type_comparison: this._generateMemoryComparisonChart(analysis.comparative),
          score_distribution: this._generateScoreDistributionChart(testResults),
          regression_detection: this._generateRegressionChart(analysis.regression)
        }
      };

      defaultLogger.info('Dashboard generated successfully', {
        metricsCount: Object.keys(dashboard.summary_metrics).length,
        chartsCount: Object.keys(dashboard.charts).length
      });

      return dashboard;
    } catch (error) {
      defaultLogger.error('Failed to generate dashboard', { error: error.message });
      throw error;
    }
  }

  /**
   * Export report to file in specified format
   * 
   * @param {Object|string} reportData - Report data or content
   * @param {string} filename - Output filename
   * @param {string} format - Export format ('json', 'csv', 'html')
   * @param {Object} [options] - Export options
   * @returns {string} Path to exported file
   */
  async exportToFile(reportData, filename, format, options = {}) {
    try {
      const outputDir = options.outputDirectory || this.options.outputDirectory;
      
      // Ensure output directory exists
      await fs.mkdir(outputDir, { recursive: true });
      
      const filePath = path.join(outputDir, filename);
      let content;

      switch (format.toLowerCase()) {
        case 'json':
          content = JSON.stringify(reportData, null, 2);
          break;
        case 'csv':
          content = this._convertToCSV(reportData);
          break;
        case 'html':
          content = typeof reportData === 'string' ? reportData : this._convertToHTML(reportData);
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      await fs.writeFile(filePath, content, 'utf8');
      
      defaultLogger.info('Report exported successfully', {
        filePath,
        format,
        contentSize: content.length
      });

      return filePath;
    } catch (error) {
      defaultLogger.error('Failed to export report', { 
        filename, 
        format, 
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * Generate multiple report formats simultaneously
   * 
   * @param {Array} testResults - Array of test result objects
   * @param {string} baseFilename - Base filename (without extension)
   * @param {Array} formats - Array of formats to generate ['json', 'csv', 'html']
   * @param {Object} [options] - Generation options
   * @returns {Object} Object with format as key and file path as value
   */
  async generateMultiFormatReports(testResults, baseFilename, formats = ['json', 'csv', 'html'], options = {}) {
    try {
      const analysis = await this.statisticalAnalyzer.analyzeTestResults(testResults);
      const results = {};

      for (const format of formats) {
        let reportData;
        let filename;

        switch (format.toLowerCase()) {
          case 'json':
            reportData = {
              testResults,
              analysis,
              metadata: {
                generatedAt: new Date().toISOString(),
                format: 'json',
                version: '1.0.0'
              }
            };
            filename = `${baseFilename}.json`;
            break;
          case 'csv':
            reportData = this._prepareCSVData(testResults, analysis);
            filename = `${baseFilename}.csv`;
            break;
          case 'html':
            reportData = await this.generateHTMLReport(testResults, analysis, options);
            filename = `${baseFilename}.html`;
            break;
          default:
            defaultLogger.warn(`Skipping unsupported format: ${format}`);
            continue;
        }

        const filePath = await this.exportToFile(reportData, filename, format, options);
        results[format] = filePath;
      }

      defaultLogger.info('Multi-format reports generated successfully', {
        baseFilename,
        formats: Object.keys(results),
        fileCount: Object.keys(results).length
      });

      return results;
    } catch (error) {
      defaultLogger.error('Failed to generate multi-format reports', { 
        baseFilename, 
        formats, 
        error: error.message 
      });
      throw error;
    }
  }

  // Private helper methods

  /**
   * Generate HTML content for the report
   */
  _generateHTMLContent(testResults, analysis, options) {
    const title = options.title || 'LLM Memory Test Report';
    const theme = options.theme || this.options.theme;
    
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        ${this._generateCSS(theme)}
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>${title}</h1>
            <div class="report-meta">
                <span>Generated: ${new Date().toLocaleString()}</span>
                <span>Test Results: ${testResults.length}</span>
                <span>Sample Size: ${analysis.metadata.sampleSize}</span>
            </div>
        </header>

        <section class="executive-summary">
            <h2>Executive Summary</h2>
            ${this._generateExecutiveSummaryHTML(analysis)}
        </section>

        <section class="performance-overview">
            <h2>Performance Overview</h2>
            ${this._generatePerformanceOverviewHTML(analysis)}
        </section>

        <section class="statistical-analysis">
            <h2>Statistical Analysis</h2>
            ${this._generateStatisticalAnalysisHTML(analysis)}
        </section>

        <section class="trends-analysis">
            <h2>Trends Analysis</h2>
            ${this._generateTrendsAnalysisHTML(analysis)}
        </section>

        <section class="regression-detection">
            <h2>Regression Detection</h2>
            ${this._generateRegressionDetectionHTML(analysis)}
        </section>

        <section class="comparative-analysis">
            <h2>Comparative Analysis</h2>
            ${this._generateComparativeAnalysisHTML(analysis)}
        </section>

        <section class="recommendations">
            <h2>Recommendations</h2>
            ${this._generateRecommendationsHTML(analysis)}
        </section>

        <section class="charts">
            <h2>Visualizations</h2>
            ${this._generateChartsHTML(testResults, analysis)}
        </section>

        <section class="detailed-results">
            <h2>Detailed Results</h2>
            ${this._generateDetailedResultsHTML(testResults)}
        </section>
    </div>

    <script>
        ${this._generateJavaScript(testResults, analysis)}
    </script>
</body>
</html>`;
  }

  /**
   * Generate CSS styles for the HTML report
   */
  _generateCSS(theme) {
    const themes = {
      default: {
        primary: '#2563eb',
        secondary: '#64748b',
        success: '#10b981',
        warning: '#f59e0b',
        danger: '#ef4444',
        background: '#ffffff',
        surface: '#f8fafc',
        text: '#1e293b'
      },
      dark: {
        primary: '#3b82f6',
        secondary: '#94a3b8',
        success: '#34d399',
        warning: '#fbbf24',
        danger: '#f87171',
        background: '#0f172a',
        surface: '#1e293b',
        text: '#f1f5f9'
      }
    };

    const colors = themes[theme] || themes.default;

    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: ${colors.text};
            background-color: ${colors.background};
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .report-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: ${colors.surface};
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .report-header h1 {
            color: ${colors.primary};
            margin-bottom: 15px;
            font-size: 2.5rem;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            color: ${colors.secondary};
        }

        section {
            margin-bottom: 40px;
            padding: 25px;
            background: ${colors.surface};
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        h2 {
            color: ${colors.primary};
            margin-bottom: 20px;
            font-size: 1.8rem;
            border-bottom: 2px solid ${colors.primary};
            padding-bottom: 10px;
        }

        h3 {
            color: ${colors.secondary};
            margin: 20px 0 10px 0;
            font-size: 1.3rem;
        }

        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .metric-card {
            padding: 20px;
            background: ${colors.background};
            border-radius: 6px;
            border-left: 4px solid ${colors.primary};
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: ${colors.primary};
        }

        .metric-label {
            color: ${colors.secondary};
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .chart-container {
            margin: 20px 0;
            padding: 20px;
            background: ${colors.background};
            border-radius: 6px;
        }

        .chart-canvas {
            max-height: 400px;
        }

        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: ${colors.success};
            color: ${colors.success};
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: ${colors.warning};
            color: ${colors.warning};
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            border-color: ${colors.danger};
            color: ${colors.danger};
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid ${colors.secondary}33;
        }

        .table th {
            background: ${colors.background};
            font-weight: 600;
            color: ${colors.primary};
        }

        .table tr:hover {
            background: ${colors.background}66;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: ${colors.background};
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, ${colors.primary}, ${colors.success});
            transition: width 0.3s ease;
        }

        .recommendation {
            padding: 15px;
            margin: 10px 0;
            background: ${colors.background};
            border-radius: 6px;
            border-left: 4px solid ${colors.primary};
        }

        .recommendation-priority {
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.8rem;
            margin-bottom: 5px;
        }

        .priority-high { color: ${colors.danger}; }
        .priority-medium { color: ${colors.warning}; }
        .priority-low { color: ${colors.secondary}; }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .report-meta {
                flex-direction: column;
                gap: 10px;
            }
            
            .metric-grid {
                grid-template-columns: 1fr;
            }
        }
    `;
  }

  /**
   * Generate executive summary HTML
   */
  _generateExecutiveSummaryHTML(analysis) {
    const overallScore = analysis.descriptive.overall?.mean || 0;
    const sampleSize = analysis.metadata.sampleSize;
    const confidenceInterval = analysis.confidence.overall;
    
    let performanceLevel = 'Poor';
    let performanceClass = 'alert-danger';
    
    if (overallScore >= 80) {
      performanceLevel = 'Excellent';
      performanceClass = 'alert-success';
    } else if (overallScore >= 60) {
      performanceLevel = 'Good';
      performanceClass = 'alert-success';
    } else if (overallScore >= 40) {
      performanceLevel = 'Fair';
      performanceClass = 'alert-warning';
    }

    return `
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">${overallScore.toFixed(1)}%</div>
                <div class="metric-label">Overall Performance</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${sampleSize}</div>
                <div class="metric-label">Test Samples</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${confidenceInterval.confidenceLevel * 100}%</div>
                <div class="metric-label">Confidence Level</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">±${confidenceInterval.marginOfError?.toFixed(1) || 0}</div>
                <div class="metric-label">Margin of Error</div>
            </div>
        </div>
        
        <div class="alert ${performanceClass}">
            <strong>Performance Assessment: ${performanceLevel}</strong><br>
            ${confidenceInterval.interpretation}
        </div>
    `;
  }

  /**
   * Generate performance overview HTML
   */
  _generatePerformanceOverviewHTML(analysis) {
    const stats = analysis.descriptive;
    
    return `
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">${stats.simple?.mean?.toFixed(1) || 0}%</div>
                <div class="metric-label">Simple Facts Average</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${stats.simple?.mean || 0}%"></div>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${stats.complex?.mean?.toFixed(1) || 0}%</div>
                <div class="metric-label">Complex Facts Average</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${stats.complex?.mean || 0}%"></div>
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${stats.overall?.standardDeviation?.toFixed(1) || 0}</div>
                <div class="metric-label">Standard Deviation</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${stats.overall?.coefficientOfVariation ? (stats.overall.coefficientOfVariation * 100).toFixed(1) : 0}%</div>
                <div class="metric-label">Coefficient of Variation</div>
            </div>
        </div>
    `;
  }

  /**
   * Generate statistical analysis HTML
   */
  _generateStatisticalAnalysisHTML(analysis) {
    const stats = analysis.descriptive.overall;
    const significance = analysis.significance;
    
    return `
        <h3>Descriptive Statistics</h3>
        <table class="table">
            <tr><th>Metric</th><th>Value</th><th>Interpretation</th></tr>
            <tr><td>Mean</td><td>${stats?.mean?.toFixed(2) || 0}</td><td>Average performance score</td></tr>
            <tr><td>Median</td><td>${stats?.median?.toFixed(2) || 0}</td><td>Middle value when sorted</td></tr>
            <tr><td>Standard Deviation</td><td>${stats?.standardDeviation?.toFixed(2) || 0}</td><td>Measure of variability</td></tr>
            <tr><td>Skewness</td><td>${stats?.skewness?.toFixed(2) || 0}</td><td>Distribution asymmetry</td></tr>
            <tr><td>Kurtosis</td><td>${stats?.kurtosis?.toFixed(2) || 0}</td><td>Distribution tail heaviness</td></tr>
        </table>

        <h3>Distribution Analysis</h3>
        <div class="alert ${significance.normality?.isNormal ? 'alert-success' : 'alert-warning'}">
            <strong>Normality Test:</strong> ${significance.normality?.interpretation || 'No data available'}
        </div>
        
        <div class="alert ${significance.outliers?.count > 0 ? 'alert-warning' : 'alert-success'}">
            <strong>Outlier Detection:</strong> ${significance.outliers?.count || 0} outliers detected (${significance.outliers?.percentage || 0}% of data)
        </div>
    `;
  }

  /**
   * Generate trends analysis HTML
   */
  _generateTrendsAnalysisHTML(analysis) {
    const trends = analysis.trends;
    
    if (trends.insufficient_data) {
      return `<div class="alert alert-warning">Insufficient data for trend analysis. At least 3 data points required.</div>`;
    }

    const getTrendIcon = (direction) => {
      switch (direction) {
        case 'improving': return '📈';
        case 'declining': return '📉';
        case 'stable': return '➡️';
        default: return '❓';
      }
    };

    const getTrendClass = (direction) => {
      switch (direction) {
        case 'improving': return 'alert-success';
        case 'declining': return 'alert-danger';
        case 'stable': return 'alert-warning';
        default: return 'alert-warning';
      }
    };

    return `
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">${getTrendIcon(trends.overall?.direction)} ${trends.overall?.direction || 'Unknown'}</div>
                <div class="metric-label">Overall Trend</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${trends.overall?.strength || 'Unknown'}</div>
                <div class="metric-label">Trend Strength</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${trends.overall?.rSquared?.toFixed(3) || 0}</div>
                <div class="metric-label">R-Squared</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${trends.volatility?.toFixed(2) || 0}</div>
                <div class="metric-label">Volatility</div>
            </div>
        </div>

        <div class="alert ${getTrendClass(trends.overall?.direction)}">
            <strong>Trend Analysis:</strong> Performance is ${trends.overall?.direction || 'unknown'} with ${trends.overall?.strength || 'unknown'} strength.
        </div>
    `;
  }

  /**
   * Generate regression detection HTML
   */
  _generateRegressionDetectionHTML(analysis) {
    const regression = analysis.regression;
    
    if (regression.insufficient_data) {
      return `<div class="alert alert-warning">Insufficient data for regression detection. At least 5 data points required.</div>`;
    }

    const alerts = regression.alerts || [];
    
    return `
        <h3>Performance Changes</h3>
        <table class="table">
            <tr><th>Category</th><th>Baseline</th><th>Recent</th><th>Change</th><th>Status</th></tr>
            ${Object.entries(regression.detection).map(([category, data]) => `
                <tr>
                    <td>${category}</td>
                    <td>${data.baselineMean?.toFixed(1) || 0}%</td>
                    <td>${data.recentMean?.toFixed(1) || 0}%</td>
                    <td class="${data.isRegression ? 'priority-high' : data.isImprovement ? 'priority-low' : ''}">${data.percentageChange}%</td>
                    <td>${data.isRegression ? '⚠️ Regression' : data.isImprovement ? '✅ Improvement' : '➡️ Stable'}</td>
                </tr>
            `).join('')}
        </table>

        <h3>Alerts</h3>
        ${alerts.length === 0 ? 
          '<div class="alert alert-success">No performance regressions detected.</div>' :
          alerts.map(alert => `
            <div class="alert alert-${alert.severity === 'high' ? 'danger' : alert.severity === 'medium' ? 'warning' : 'success'}">
              <strong>${alert.type.toUpperCase()}:</strong> ${alert.message}<br>
              <em>Recommendation: ${alert.recommendation}</em>
            </div>
          `).join('')
        }
    `;
  }

  /**
   * Generate comparative analysis HTML
   */
  _generateComparativeAnalysisHTML(analysis) {
    const comparative = analysis.comparative;
    
    if (comparative.insufficient_data) {
      return `<div class="alert alert-warning">Insufficient data for comparative analysis. At least 2 different memory types required.</div>`;
    }

    const rankings = comparative.rankings.overall || [];
    
    return `
        <h3>Memory Type Rankings</h3>
        <table class="table">
            <tr><th>Rank</th><th>Memory Type</th><th>Average Score</th><th>Sample Size</th><th>Performance</th></tr>
            ${rankings.map((item, index) => `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.memoryType}</td>
                    <td>${item.mean?.toFixed(1) || 0}%</td>
                    <td>${item.sampleSize}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${item.mean || 0}%"></div>
                        </div>
                    </td>
                </tr>
            `).join('')}
        </table>

        <h3>Statistical Significance</h3>
        ${Object.entries(comparative.significance.overall || {}).map(([comparison, test]) => `
            <div class="alert ${test.isSignificant ? 'alert-success' : 'alert-warning'}">
                <strong>${comparison.replace('_vs_', ' vs ')}:</strong> ${test.interpretation}<br>
                <em>p-value: ${test.pValue}, t-statistic: ${test.tStatistic?.toFixed(3) || 0}</em>
            </div>
        `).join('')}
    `;
  }

  /**
   * Generate recommendations HTML
   */
  _generateRecommendationsHTML(analysis) {
    const recommendations = analysis.recommendations || [];
    
    if (recommendations.length === 0) {
      return `<div class="alert alert-success">No specific recommendations at this time. Performance appears to be within acceptable parameters.</div>`;
    }

    return recommendations.map(rec => `
        <div class="recommendation">
            <div class="recommendation-priority priority-${rec.priority}">${rec.priority} Priority</div>
            <strong>${rec.message}</strong><br>
            <em>${rec.details}</em>
        </div>
    `).join('');
  }

  /**
   * Generate charts HTML
   */
  _generateChartsHTML(testResults, analysis) {
    return `
        <div class="chart-container">
            <h3>Performance Over Time</h3>
            <canvas id="performanceChart" class="chart-canvas"></canvas>
        </div>
        
        <div class="chart-container">
            <h3>Score Distribution</h3>
            <canvas id="distributionChart" class="chart-canvas"></canvas>
        </div>
        
        <div class="chart-container">
            <h3>Memory Type Comparison</h3>
            <canvas id="comparisonChart" class="chart-canvas"></canvas>
        </div>
    `;
  }

  /**
   * Generate detailed results HTML
   */
  _generateDetailedResultsHTML(testResults) {
    const recentResults = testResults.slice(-10); // Show last 10 results
    
    return `
        <table class="table">
            <tr>
                <th>Timestamp</th>
                <th>Memory Type</th>
                <th>Overall Score</th>
                <th>Simple Facts</th>
                <th>Complex Facts</th>
                <th>Session ID</th>
            </tr>
            ${recentResults.map(result => `
                <tr>
                    <td>${new Date(result.metadata?.timestamp || result.timestamp || Date.now()).toLocaleString()}</td>
                    <td>${result.metadata?.memoryType || result.memoryType || 'Unknown'}</td>
                    <td>${result.scores?.overall?.toFixed(1) || 0}%</td>
                    <td>${result.scores?.simple?.toFixed(1) || 0}%</td>
                    <td>${result.scores?.complex?.toFixed(1) || 0}%</td>
                    <td>${result.metadata?.sessionId || result.sessionId || 'N/A'}</td>
                </tr>
            `).join('')}
        </table>
        
        ${testResults.length > 10 ? `<p><em>Showing last 10 of ${testResults.length} total results.</em></p>` : ''}
    `;
  }

  /**
   * Generate JavaScript for interactive features
   */
  _generateJavaScript(testResults, analysis) {
    return `
        // Performance over time chart
        const performanceCtx = document.getElementById('performanceChart').getContext('2d');
        new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: ${JSON.stringify(testResults.map((_, i) => `Test ${i + 1}`))},
                datasets: [{
                    label: 'Overall Score',
                    data: ${JSON.stringify(testResults.map(r => r.scores?.overall || 0))},
                    borderColor: '#2563eb',
                    backgroundColor: 'rgba(37, 99, 235, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Simple Facts',
                    data: ${JSON.stringify(testResults.map(r => r.scores?.simple || 0))},
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Complex Facts',
                    data: ${JSON.stringify(testResults.map(r => r.scores?.complex || 0))},
                    borderColor: '#f59e0b',
                    backgroundColor: 'rgba(245, 158, 11, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        // Score distribution chart
        const distributionCtx = document.getElementById('distributionChart').getContext('2d');
        const scores = ${JSON.stringify(testResults.map(r => r.scores?.overall || 0))};
        const bins = [0, 20, 40, 60, 80, 100];
        const binCounts = bins.slice(0, -1).map((bin, i) => 
            scores.filter(score => score >= bin && score < bins[i + 1]).length
        );
        
        new Chart(distributionCtx, {
            type: 'bar',
            data: {
                labels: ['0-20%', '20-40%', '40-60%', '60-80%', '80-100%'],
                datasets: [{
                    label: 'Frequency',
                    data: binCounts,
                    backgroundColor: 'rgba(37, 99, 235, 0.6)',
                    borderColor: '#2563eb',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Memory type comparison chart
        const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
        const memoryTypes = {};
        ${JSON.stringify(testResults)}.forEach(result => {
            const type = result.metadata?.memoryType || result.memoryType || 'unknown';
            if (!memoryTypes[type]) memoryTypes[type] = [];
            memoryTypes[type].push(result.scores?.overall || 0);
        });
        
        const memoryLabels = Object.keys(memoryTypes);
        const memoryAverages = memoryLabels.map(type => 
            memoryTypes[type].reduce((a, b) => a + b, 0) / memoryTypes[type].length
        );
        
        new Chart(comparisonCtx, {
            type: 'bar',
            data: {
                labels: memoryLabels,
                datasets: [{
                    label: 'Average Score',
                    data: memoryAverages,
                    backgroundColor: memoryLabels.map((_, i) => 
                        \`hsl(\${i * 360 / memoryLabels.length}, 70%, 50%)\`
                    ),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
    `;
  }

  /**
   * Convert data to CSV format
   */
  _convertToCSV(data) {
    if (Array.isArray(data)) {
      // Handle array of test results
      const headers = ['timestamp', 'memoryType', 'overallScore', 'simpleScore', 'complexScore', 'sessionId'];
      const csvRows = [headers.join(',')];
      
      data.forEach(result => {
        const row = [
          result.metadata?.timestamp || result.timestamp || '',
          result.metadata?.memoryType || result.memoryType || '',
          result.scores?.overall || 0,
          result.scores?.simple || 0,
          result.scores?.complex || 0,
          result.metadata?.sessionId || result.sessionId || ''
        ];
        csvRows.push(row.join(','));
      });
      
      return csvRows.join('\n');
    } else {
      // Handle analysis object
      return this._objectToCSV(data);
    }
  }

  /**
   * Convert object to CSV format
   */
  _objectToCSV(obj, prefix = '') {
    const rows = [];
    
    for (const [key, value] of Object.entries(obj)) {
      const fullKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        rows.push(...this._objectToCSV(value, fullKey));
      } else {
        rows.push(`"${fullKey}","${value}"`);
      }
    }
    
    return rows.join('\n');
  }

  /**
   * Convert data to HTML format
   */
  _convertToHTML(data) {
    return `
      <html>
        <head><title>Report Data</title></head>
        <body>
          <pre>${JSON.stringify(data, null, 2)}</pre>
        </body>
      </html>
    `;
  }

  /**
   * Prepare CSV data structure
   */
  _prepareCSVData(testResults, analysis) {
    return testResults;
  }

  /**
   * Generate executive summary
   */
  _generateExecutiveSummary(analysis) {
    const overallScore = analysis.descriptive.overall?.mean || 0;
    const trend = analysis.trends?.overall?.direction || 'stable';
    const regressionAlerts = analysis.regression?.alerts?.filter(a => a.type === 'regression').length || 0;
    
    return {
      overallPerformance: `${overallScore.toFixed(1)}%`,
      performanceLevel: overallScore >= 80 ? 'Excellent' : overallScore >= 60 ? 'Good' : overallScore >= 40 ? 'Fair' : 'Poor',
      trend: trend,
      regressionAlerts: regressionAlerts,
      sampleSize: analysis.metadata.sampleSize,
      confidenceLevel: `${(analysis.confidence.overall.confidenceLevel * 100)}%`,
      keyInsights: [
        `Average performance: ${overallScore.toFixed(1)}%`,
        `Performance trend: ${trend}`,
        regressionAlerts > 0 ? `${regressionAlerts} regression alerts` : 'No regressions detected',
        `Based on ${analysis.metadata.sampleSize} test samples`
      ]
    };
  }

  /**
   * Generate performance rankings
   */
  _generatePerformanceRankings(comparative) {
    return comparative.rankings || {};
  }

  /**
   * Generate significance report
   */
  _generateSignificanceReport(comparative) {
    return comparative.significance || {};
  }

  /**
   * Generate comparative recommendations
   */
  _generateComparativeRecommendations(analysis) {
    return analysis.comparative.recommendations || [];
  }

  /**
   * Generate chart data
   */
  _generateChartData(testResults, analysis) {
    return {
      performanceOverTime: testResults.map((result, index) => ({
        x: index,
        overall: result.scores?.overall || 0,
        simple: result.scores?.simple || 0,
        complex: result.scores?.complex || 0
      })),
      memoryTypeComparison: this._generateMemoryComparisonChart(analysis.comparative),
      scoreDistribution: this._generateScoreDistributionChart(testResults),
      trendAnalysis: analysis.trends
    };
  }

  /**
   * Generate summary metrics
   */
  _generateSummaryMetrics(analysis) {
    return {
      overallScore: analysis.descriptive.overall?.mean || 0,
      simpleFactsScore: analysis.descriptive.simple?.mean || 0,
      complexFactsScore: analysis.descriptive.complex?.mean || 0,
      standardDeviation: analysis.descriptive.overall?.standardDeviation || 0,
      sampleSize: analysis.metadata.sampleSize,
      confidenceInterval: analysis.confidence.overall,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Generate trend data
   */
  _generateTrendData(trends) {
    if (trends.insufficient_data) return null;
    
    return {
      overall: trends.overall,
      simple: trends.simple,
      complex: trends.complex,
      volatility: trends.volatility,
      timespan: trends.timespan
    };
  }

  /**
   * Generate regression alerts
   */
  _generateRegressionAlerts(regression) {
    if (regression.insufficient_data) return [];
    
    return regression.alerts || [];
  }

  /**
   * Generate recent tests data
   */
  _generateRecentTestsData(testResults) {
    return testResults.slice(-10).map(result => ({
      timestamp: result.metadata?.timestamp || result.timestamp,
      memoryType: result.metadata?.memoryType || result.memoryType,
      overallScore: result.scores?.overall || 0,
      sessionId: result.metadata?.sessionId || result.sessionId
    }));
  }

  /**
   * Generate memory comparison data
   */
  _generateMemoryComparisonData(comparative) {
    if (comparative.insufficient_data) return null;
    
    return comparative.rankings.overall || [];
  }

  /**
   * Generate system health data
   */
  _generateSystemHealthData(testResults) {
    const recent = testResults.slice(-5);
    const avgScore = recent.reduce((sum, r) => sum + (r.scores?.overall || 0), 0) / recent.length;
    
    return {
      status: avgScore >= 70 ? 'healthy' : avgScore >= 50 ? 'warning' : 'critical',
      averageScore: avgScore,
      recentTests: recent.length,
      lastTest: recent[recent.length - 1]?.metadata?.timestamp || recent[recent.length - 1]?.timestamp
    };
  }

  /**
   * Generate performance time chart data
   */
  _generatePerformanceTimeChart(testResults) {
    return {
      type: 'line',
      data: testResults.map((result, index) => ({
        x: index,
        y: result.scores?.overall || 0,
        timestamp: result.metadata?.timestamp || result.timestamp
      }))
    };
  }

  /**
   * Generate memory comparison chart data
   */
  _generateMemoryComparisonChart(comparative) {
    if (comparative.insufficient_data) return null;
    
    const rankings = comparative.rankings.overall || [];
    return {
      type: 'bar',
      data: rankings.map(item => ({
        label: item.memoryType,
        value: item.mean,
        sampleSize: item.sampleSize
      }))
    };
  }

  /**
   * Generate score distribution chart data
   */
  _generateScoreDistributionChart(testResults) {
    const scores = testResults.map(r => r.scores?.overall || 0);
    const bins = [0, 20, 40, 60, 80, 100];
    const distribution = bins.slice(0, -1).map((bin, i) => ({
      range: `${bin}-${bins[i + 1]}%`,
      count: scores.filter(score => score >= bin && score < bins[i + 1]).length
    }));
    
    return {
      type: 'histogram',
      data: distribution
    };
  }

  /**
   * Generate regression chart data
   */
  _generateRegressionChart(regression) {
    if (regression.insufficient_data) return null;
    
    return {
      type: 'comparison',
      data: Object.entries(regression.detection).map(([category, data]) => ({
        category,
        baseline: data.baselineMean,
        recent: data.recentMean,
        change: data.percentageChange,
        isRegression: data.isRegression
      }))
    };
  }

  /**
   * Calculate time range for test results
   */
  _calculateTimeRange(testResults) {
    if (testResults.length === 0) return null;
    
    const timestamps = testResults
      .map(r => r.metadata?.timestamp || r.timestamp)
      .filter(t => t && t !== '0' && t !== 0)
      .map(t => new Date(t))
      .filter(d => !isNaN(d.getTime()))
      .sort((a, b) => a - b);
    
    if (timestamps.length === 0) return null;
    
    return {
      start: timestamps[0].toISOString(),
      end: timestamps[timestamps.length - 1].toISOString(),
      duration: timestamps[timestamps.length - 1] - timestamps[0]
    };
  }
}

module.exports = {
  ReportGenerator
};