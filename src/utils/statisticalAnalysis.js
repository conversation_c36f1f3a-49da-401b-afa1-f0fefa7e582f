/**
 * Statistical Analysis Tools - Advanced Statistical Analysis for Test Results
 *
 * This module provides comprehensive statistical analysis capabilities for
 * LLM memory test results, including trend analysis, confidence intervals,
 * significance testing, and performance regression detection.
 *
 * Features:
 * - Descriptive statistics calculation
 * - Trend analysis for memory performance across multiple runs
 * - Confidence intervals and significance testing
 * - Performance regression detection and alerting
 * - Comparative analysis between different memory implementations
 * - Statistical hypothesis testing
 * - Time series analysis for performance trends
 *
 * @module StatisticalAnalysis
 * 
 * @example
 * const analyzer = new StatisticalAnalyzer();
 * const results = analyzer.analyzeTestResults(testResultsArray);
 * console.log(`Mean score: ${results.descriptive.mean}`);
 * console.log(`Confidence interval: [${results.confidence.lower}, ${results.confidence.upper}]`);
 */

const fs = require('fs').promises;
const path = require('path');
const { defaultLogger } = require('./logger');

/**
 * Statistical Analysis Class
 * 
 * Provides comprehensive statistical analysis capabilities for test results
 * including descriptive statistics, trend analysis, confidence intervals,
 * and regression detection.
 */
class StatisticalAnalyzer {
  /**
   * Creates a new StatisticalAnalyzer instance
   * 
   * @param {Object} [options] - Configuration options
   * @param {number} [options.confidenceLevel=0.95] - Confidence level for intervals
   * @param {number} [options.significanceLevel=0.05] - Significance level for tests
   * @param {number} [options.regressionThreshold=0.1] - Threshold for regression detection (10%)
   * @param {boolean} [options.enableDetailedLogging=false] - Enable detailed logging
   */
  constructor(options = {}) {
    this.options = {
      confidenceLevel: 0.95,
      significanceLevel: 0.05,
      regressionThreshold: 0.1,
      enableDetailedLogging: false,
      ...options
    };

    this.zScores = {
      0.90: 1.645,
      0.95: 1.96,
      0.99: 2.576
    };

    defaultLogger.info('StatisticalAnalyzer initialized', {
      options: this.options
    });
  }

  /**
   * Analyze test results with comprehensive statistics
   * 
   * @param {Array} testResults - Array of test result objects
   * @param {Object} [options] - Analysis options
   * @returns {Object} Comprehensive statistical analysis
   */
  async analyzeTestResults(testResults, options = {}) {
    if (!Array.isArray(testResults) || testResults.length === 0) {
      throw new Error('Test results must be a non-empty array');
    }

    const analysis = {
      metadata: {
        analysisTimestamp: new Date().toISOString(),
        sampleSize: testResults.length,
        analysisOptions: { ...this.options, ...options }
      },
      descriptive: this.calculateDescriptiveStatistics(testResults),
      confidence: this.calculateConfidenceIntervals(testResults),
      trends: this.analyzeTrends(testResults),
      regression: this.detectPerformanceRegression(testResults),
      comparative: this.performComparativeAnalysis(testResults),
      significance: this.performSignificanceTests(testResults),
      recommendations: this.generateRecommendations(testResults)
    };

    if (this.options.enableDetailedLogging) {
      defaultLogger.debug('Statistical analysis completed', {
        sampleSize: testResults.length,
        meanScore: analysis.descriptive.overall.mean,
        confidenceInterval: analysis.confidence.overall
      });
    }

    return analysis;
  }

  /**
   * Calculate descriptive statistics for test results
   * 
   * @param {Array} testResults - Test results array
   * @returns {Object} Descriptive statistics
   */
  calculateDescriptiveStatistics(testResults) {
    const scores = {
      overall: testResults.map(r => r.scores?.overall || 0),
      simple: testResults.map(r => r.scores?.simple || 0),
      complex: testResults.map(r => r.scores?.complex || 0)
    };

    const stats = {};
    
    for (const [category, values] of Object.entries(scores)) {
      stats[category] = {
        count: values.length,
        mean: this._calculateMean(values),
        median: this._calculateMedian(values),
        mode: this._calculateMode(values),
        standardDeviation: this._calculateStandardDeviation(values),
        variance: this._calculateVariance(values),
        min: Math.min(...values),
        max: Math.max(...values),
        range: Math.max(...values) - Math.min(...values),
        quartiles: this._calculateQuartiles(values),
        skewness: this._calculateSkewness(values),
        kurtosis: this._calculateKurtosis(values),
        coefficientOfVariation: this._calculateCoefficientOfVariation(values)
      };
    }

    // Add performance metrics if available
    if (testResults[0]?.performanceMetrics) {
      stats.performance = this._analyzePerformanceMetrics(testResults);
    }

    return stats;
  }

  /**
   * Calculate confidence intervals for test results
   * 
   * @param {Array} testResults - Test results array
   * @returns {Object} Confidence intervals
   */
  calculateConfidenceIntervals(testResults) {
    const confidenceLevel = this.options.confidenceLevel;
    const zScore = this.zScores[confidenceLevel] || 1.96;

    const scores = {
      overall: testResults.map(r => r.scores?.overall || 0),
      simple: testResults.map(r => r.scores?.simple || 0),
      complex: testResults.map(r => r.scores?.complex || 0)
    };

    const intervals = {};

    for (const [category, values] of Object.entries(scores)) {
      const mean = this._calculateMean(values);
      const standardError = this._calculateStandardDeviation(values) / Math.sqrt(values.length);
      const marginOfError = zScore * standardError;

      intervals[category] = {
        mean,
        standardError,
        marginOfError,
        lower: mean - marginOfError,
        upper: mean + marginOfError,
        confidenceLevel,
        interpretation: this._interpretConfidenceInterval(mean, marginOfError, confidenceLevel)
      };
    }

    return intervals;
  }

  /**
   * Analyze trends in test results over time
   * 
   * @param {Array} testResults - Test results array (should be chronologically ordered)
   * @returns {Object} Trend analysis
   */
  analyzeTrends(testResults) {
    if (testResults.length < 3) {
      return {
        insufficient_data: true,
        message: 'At least 3 data points required for trend analysis'
      };
    }

    // Sort by timestamp if available, otherwise use array order
    const sortedResults = testResults.sort((a, b) => {
      const timeA = new Date(a.metadata?.timestamp || a.timestamp || 0);
      const timeB = new Date(b.metadata?.timestamp || b.timestamp || 0);
      return timeA - timeB;
    });

    const trends = {
      overall: this._calculateTrend(sortedResults.map(r => r.scores?.overall || 0)),
      simple: this._calculateTrend(sortedResults.map(r => r.scores?.simple || 0)),
      complex: this._calculateTrend(sortedResults.map(r => r.scores?.complex || 0)),
      timespan: this._calculateTimespan(sortedResults),
      seasonality: this._detectSeasonality(sortedResults),
      volatility: this._calculateVolatility(sortedResults)
    };

    // Add performance trends if available
    if (sortedResults[0]?.performanceMetrics) {
      trends.performance = this._analyzePerformanceTrends(sortedResults);
    }

    return trends;
  }

  /**
   * Detect performance regression in test results
   * 
   * @param {Array} testResults - Test results array
   * @returns {Object} Regression analysis
   */
  detectPerformanceRegression(testResults) {
    if (testResults.length < 5) {
      return {
        insufficient_data: true,
        message: 'At least 5 data points required for regression detection'
      };
    }

    const threshold = this.options.regressionThreshold;
    const recentCount = Math.min(5, Math.floor(testResults.length * 0.3));
    const baselineCount = Math.min(10, Math.floor(testResults.length * 0.5));

    // Sort by timestamp
    const sortedResults = testResults.sort((a, b) => {
      const timeA = new Date(a.metadata?.timestamp || a.timestamp || 0);
      const timeB = new Date(b.metadata?.timestamp || b.timestamp || 0);
      return timeA - timeB;
    });

    const baseline = sortedResults.slice(0, baselineCount);
    const recent = sortedResults.slice(-recentCount);

    const regression = {
      baseline: {
        period: `${baseline.length} tests`,
        scores: this.calculateDescriptiveStatistics(baseline)
      },
      recent: {
        period: `${recent.length} tests`,
        scores: this.calculateDescriptiveStatistics(recent)
      },
      detection: {},
      alerts: []
    };

    // Check for regression in each category
    const categories = ['overall', 'simple', 'complex'];
    
    for (const category of categories) {
      const baselineMean = regression.baseline.scores[category]?.mean || 0;
      const recentMean = regression.recent.scores[category]?.mean || 0;
      const change = (recentMean - baselineMean) / baselineMean;
      
      const isRegression = change < -threshold;
      const isImprovement = change > threshold;

      regression.detection[category] = {
        baselineMean,
        recentMean,
        absoluteChange: recentMean - baselineMean,
        percentageChange: (change * 100).toFixed(2),
        isRegression,
        isImprovement,
        significance: this._testSignificance(
          baseline.map(r => r.scores?.[category] || 0),
          recent.map(r => r.scores?.[category] || 0)
        )
      };

      if (isRegression) {
        regression.alerts.push({
          type: 'regression',
          category,
          severity: Math.abs(change) > threshold * 2 ? 'high' : 'medium',
          message: `${category} scores decreased by ${Math.abs(change * 100).toFixed(1)}%`,
          recommendation: this._generateRegressionRecommendation(category, change)
        });
      } else if (isImprovement) {
        regression.alerts.push({
          type: 'improvement',
          category,
          severity: 'info',
          message: `${category} scores improved by ${(change * 100).toFixed(1)}%`,
          recommendation: 'Consider documenting the changes that led to this improvement'
        });
      }
    }

    return regression;
  }

  /**
   * Perform comparative analysis between different memory implementations
   * 
   * @param {Array} testResults - Test results array
   * @returns {Object} Comparative analysis
   */
  performComparativeAnalysis(testResults) {
    // Group results by memory type
    const groupedResults = {};
    
    for (const result of testResults) {
      const memoryType = result.metadata?.memoryType || result.memoryType || 'unknown';
      if (!groupedResults[memoryType]) {
        groupedResults[memoryType] = [];
      }
      groupedResults[memoryType].push(result);
    }

    const memoryTypes = Object.keys(groupedResults);
    
    if (memoryTypes.length < 2) {
      return {
        insufficient_data: true,
        message: 'At least 2 different memory types required for comparative analysis'
      };
    }

    const comparison = {
      memoryTypes,
      statistics: {},
      rankings: {},
      significance: {},
      recommendations: []
    };

    // Calculate statistics for each memory type
    for (const [memoryType, results] of Object.entries(groupedResults)) {
      comparison.statistics[memoryType] = {
        sampleSize: results.length,
        descriptive: this.calculateDescriptiveStatistics(results),
        confidence: this.calculateConfidenceIntervals(results)
      };
    }

    // Rank memory types by performance
    const categories = ['overall', 'simple', 'complex'];
    
    for (const category of categories) {
      const rankings = memoryTypes
        .map(type => ({
          memoryType: type,
          mean: comparison.statistics[type].descriptive[category]?.mean || 0,
          sampleSize: comparison.statistics[type].sampleSize
        }))
        .sort((a, b) => b.mean - a.mean);

      comparison.rankings[category] = rankings;

      // Perform pairwise significance tests
      comparison.significance[category] = this._performPairwiseTests(groupedResults, category);
    }

    // Generate comparative recommendations
    comparison.recommendations = this._generateComparativeRecommendations(comparison);

    return comparison;
  }

  /**
   * Perform significance tests on test results
   * 
   * @param {Array} testResults - Test results array
   * @returns {Object} Significance test results
   */
  performSignificanceTests(testResults) {
    const tests = {
      normality: this._testNormality(testResults),
      outliers: this._detectOutliers(testResults),
      homogeneity: this._testHomogeneity(testResults),
      independence: this._testIndependence(testResults)
    };

    return tests;
  }

  /**
   * Generate recommendations based on statistical analysis
   * 
   * @param {Array} testResults - Test results array
   * @returns {Array} Array of recommendations
   */
  generateRecommendations(testResults) {
    const recommendations = [];
    const stats = this.calculateDescriptiveStatistics(testResults);

    // Sample size recommendations
    if (testResults.length < 10) {
      recommendations.push({
        type: 'sample_size',
        priority: 'high',
        message: 'Increase sample size for more reliable statistical analysis',
        details: `Current sample size: ${testResults.length}. Recommended: at least 30 for robust statistics.`
      });
    }

    // Variability recommendations
    const overallCV = stats.overall?.coefficientOfVariation || 0;
    if (overallCV > 0.3) {
      recommendations.push({
        type: 'variability',
        priority: 'medium',
        message: 'High variability detected in test results',
        details: `Coefficient of variation: ${(overallCV * 100).toFixed(1)}%. Consider investigating sources of variability.`
      });
    }

    // Performance recommendations
    if (stats.overall?.mean < 50) {
      recommendations.push({
        type: 'performance',
        priority: 'high',
        message: 'Overall performance below 50%',
        details: 'Consider optimizing memory implementations or adjusting test parameters.'
      });
    }

    // Distribution recommendations
    const skewness = Math.abs(stats.overall?.skewness || 0);
    if (skewness > 1) {
      recommendations.push({
        type: 'distribution',
        priority: 'low',
        message: 'Results show significant skewness',
        details: `Skewness: ${skewness.toFixed(2)}. Consider using median instead of mean for central tendency.`
      });
    }

    return recommendations;
  }

  /**
   * Load and analyze historical test results
   * 
   * @param {string} resultsDirectory - Directory containing test result files
   * @returns {Object} Historical analysis
   */
  async analyzeHistoricalResults(resultsDirectory) {
    try {
      const files = await fs.readdir(resultsDirectory);
      const resultFiles = files.filter(file => file.startsWith('results_') && file.endsWith('.md'));

      if (resultFiles.length === 0) {
        throw new Error('No test result files found in directory');
      }

      const historicalData = [];

      for (const file of resultFiles) {
        try {
          const filePath = path.join(resultsDirectory, file);
          const content = await fs.readFile(filePath, 'utf8');
          const parsedResult = this._parseResultFile(content, file);
          
          if (parsedResult) {
            historicalData.push(parsedResult);
          }
        } catch (error) {
          defaultLogger.warn(`Failed to parse result file ${file}`, { error: error.message });
        }
      }

      if (historicalData.length === 0) {
        throw new Error('No valid test results could be parsed');
      }

      // Sort by timestamp
      historicalData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

      const analysis = await this.analyzeTestResults(historicalData);
      
      // Add historical-specific analysis
      analysis.historical = {
        timespan: {
          start: historicalData[0].timestamp,
          end: historicalData[historicalData.length - 1].timestamp,
          duration: new Date(historicalData[historicalData.length - 1].timestamp) - new Date(historicalData[0].timestamp)
        },
        frequency: this._analyzeTestFrequency(historicalData),
        patterns: this._identifyPatterns(historicalData)
      };

      return analysis;
    } catch (error) {
      defaultLogger.error('Failed to analyze historical results', { error: error.message });
      throw error;
    }
  }

  // Private helper methods

  /**
   * Calculate mean of an array of numbers
   */
  _calculateMean(values) {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * Calculate median of an array of numbers
   */
  _calculateMedian(values) {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  /**
   * Calculate mode of an array of numbers
   */
  _calculateMode(values) {
    if (values.length === 0) return null;
    
    const frequency = {};
    let maxFreq = 0;
    let modes = [];

    for (const value of values) {
      frequency[value] = (frequency[value] || 0) + 1;
      if (frequency[value] > maxFreq) {
        maxFreq = frequency[value];
        modes = [value];
      } else if (frequency[value] === maxFreq && !modes.includes(value)) {
        modes.push(value);
      }
    }

    return modes.length === values.length ? null : modes;
  }

  /**
   * Calculate standard deviation
   */
  _calculateStandardDeviation(values) {
    if (values.length === 0) return 0;
    const mean = this._calculateMean(values);
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    const variance = this._calculateMean(squaredDiffs);
    return Math.sqrt(variance);
  }

  /**
   * Calculate variance
   */
  _calculateVariance(values) {
    if (values.length === 0) return 0;
    const mean = this._calculateMean(values);
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return this._calculateMean(squaredDiffs);
  }

  /**
   * Calculate quartiles
   */
  _calculateQuartiles(values) {
    if (values.length === 0) return { q1: 0, q2: 0, q3: 0 };
    
    const sorted = [...values].sort((a, b) => a - b);
    const n = sorted.length;
    
    const q1Index = Math.floor(n * 0.25);
    const q2Index = Math.floor(n * 0.5);
    const q3Index = Math.floor(n * 0.75);
    
    return {
      q1: sorted[q1Index],
      q2: sorted[q2Index], // median
      q3: sorted[q3Index]
    };
  }

  /**
   * Calculate skewness
   */
  _calculateSkewness(values) {
    if (values.length < 3) return 0;
    
    const mean = this._calculateMean(values);
    const stdDev = this._calculateStandardDeviation(values);
    
    if (stdDev === 0) return 0;
    
    const n = values.length;
    const skewness = values.reduce((sum, val) => {
      return sum + Math.pow((val - mean) / stdDev, 3);
    }, 0) / n;
    
    return skewness;
  }

  /**
   * Calculate kurtosis
   */
  _calculateKurtosis(values) {
    if (values.length < 4) return 0;
    
    const mean = this._calculateMean(values);
    const stdDev = this._calculateStandardDeviation(values);
    
    if (stdDev === 0) return 0;
    
    const n = values.length;
    const kurtosis = values.reduce((sum, val) => {
      return sum + Math.pow((val - mean) / stdDev, 4);
    }, 0) / n;
    
    return kurtosis - 3; // Excess kurtosis
  }

  /**
   * Calculate coefficient of variation
   */
  _calculateCoefficientOfVariation(values) {
    const mean = this._calculateMean(values);
    const stdDev = this._calculateStandardDeviation(values);
    return mean === 0 ? 0 : stdDev / mean;
  }

  /**
   * Calculate trend using linear regression
   */
  _calculateTrend(values) {
    if (values.length < 2) return { slope: 0, direction: 'insufficient_data' };
    
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = values;
    
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;
    
    // Calculate R-squared
    const yMean = this._calculateMean(y);
    const totalSumSquares = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
    const residualSumSquares = y.reduce((sum, yi, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(yi - predicted, 2);
    }, 0);
    
    const rSquared = 1 - (residualSumSquares / totalSumSquares);
    
    let direction = 'stable';
    if (Math.abs(slope) > 0.1) {
      direction = slope > 0 ? 'improving' : 'declining';
    }
    
    return {
      slope,
      intercept,
      rSquared,
      direction,
      strength: this._interpretTrendStrength(Math.abs(slope), rSquared)
    };
  }

  /**
   * Interpret confidence interval
   */
  _interpretConfidenceInterval(mean, marginOfError, confidenceLevel) {
    const precision = (marginOfError / mean) * 100;
    let interpretation = `We are ${(confidenceLevel * 100)}% confident that the true mean lies between ${(mean - marginOfError).toFixed(2)} and ${(mean + marginOfError).toFixed(2)}.`;
    
    if (precision < 5) {
      interpretation += ' This is a very precise estimate.';
    } else if (precision < 10) {
      interpretation += ' This is a reasonably precise estimate.';
    } else {
      interpretation += ' Consider increasing sample size for better precision.';
    }
    
    return interpretation;
  }

  /**
   * Test statistical significance between two groups
   */
  _testSignificance(group1, group2) {
    if (group1.length < 2 || group2.length < 2) {
      return { insufficient_data: true };
    }

    const mean1 = this._calculateMean(group1);
    const mean2 = this._calculateMean(group2);
    const std1 = this._calculateStandardDeviation(group1);
    const std2 = this._calculateStandardDeviation(group2);
    
    // Welch's t-test (unequal variances)
    const n1 = group1.length;
    const n2 = group2.length;
    
    const pooledStdError = Math.sqrt((std1 * std1) / n1 + (std2 * std2) / n2);
    const tStatistic = (mean1 - mean2) / pooledStdError;
    
    // Approximate degrees of freedom (Welch-Satterthwaite equation)
    const df = Math.pow(pooledStdError, 4) / 
      (Math.pow(std1, 4) / (n1 * n1 * (n1 - 1)) + Math.pow(std2, 4) / (n2 * n2 * (n2 - 1)));
    
    // Critical t-value for 95% confidence (approximation)
    const criticalT = 2.0; // Simplified for demonstration
    
    const isSignificant = Math.abs(tStatistic) > criticalT;
    const pValue = this._approximatePValue(Math.abs(tStatistic), df);
    
    return {
      tStatistic,
      degreesOfFreedom: Math.round(df),
      pValue,
      isSignificant,
      confidenceLevel: this.options.confidenceLevel,
      interpretation: isSignificant 
        ? 'The difference between groups is statistically significant'
        : 'No statistically significant difference detected'
    };
  }

  /**
   * Approximate p-value calculation (simplified)
   */
  _approximatePValue(tStat, df) {
    // Simplified p-value approximation
    if (tStat > 3) return 0.001;
    if (tStat > 2.5) return 0.01;
    if (tStat > 2) return 0.05;
    if (tStat > 1.5) return 0.1;
    return 0.2;
  }

  /**
   * Parse result file content
   */
  _parseResultFile(content, filename) {
    try {
      // Extract timestamp from filename
      const timestampMatch = filename.match(/results_(\d+)_/);
      const timestamp = timestampMatch ? new Date(parseInt(timestampMatch[1]) * 1000) : new Date();

      // Extract scores - try multiple patterns
      const overallMatch = content.match(/\*\*Overall Score\*\*:\s*(\d+\.?\d*)%/) || 
                          content.match(/Overall Score.*?(\d+\.?\d*)%/) ||
                          content.match(/- \*\*Overall Score\*\*:\s*(\d+\.?\d*)%/);
      const simpleMatch = content.match(/\*\*Simple Facts Score\*\*:\s*(\d+\.?\d*)%/) || 
                         content.match(/Simple Facts Score.*?(\d+\.?\d*)%/) ||
                         content.match(/- \*\*Simple Facts Score\*\*:\s*(\d+\.?\d*)%/);
      const complexMatch = content.match(/\*\*Complex Facts Score\*\*:\s*(\d+\.?\d*)%/) || 
                          content.match(/Complex Facts Score.*?(\d+\.?\d*)%/) ||
                          content.match(/- \*\*Complex Facts Score\*\*:\s*(\d+\.?\d*)%/);

      // Extract memory type
      const memoryTypeMatch = content.match(/\*\*Memory Type\*\*:\s*(\w+)/) ||
                             content.match(/Memory Type.*?(\w+)/) ||
                             content.match(/- \*\*Memory Type\*\*:\s*(\w+)/);

      // Extract session ID - try multiple patterns with proper regex
      const sessionIdMatch = content.match(/\*\*Session ID\*\*:\s*([a-f0-9-]{36})/) ||
                             content.match(/Session ID.*?([a-f0-9-]{36})/) ||
                             content.match(/- \*\*Session ID\*\*:\s*([a-f0-9-]{36})/);

      return {
        timestamp: timestamp.toISOString(),
        sessionId: sessionIdMatch ? sessionIdMatch[1] : null,
        memoryType: memoryTypeMatch ? memoryTypeMatch[1] : 'unknown',
        scores: {
          overall: overallMatch ? parseFloat(overallMatch[1]) : 0,
          simple: simpleMatch ? parseFloat(simpleMatch[1]) : 0,
          complex: complexMatch ? parseFloat(complexMatch[1]) : 0
        },
        metadata: {
          filename,
          timestamp: timestamp.toISOString(),
          memoryType: memoryTypeMatch ? memoryTypeMatch[1] : 'unknown'
        }
      };
    } catch (error) {
      defaultLogger.warn(`Failed to parse result file ${filename}`, { error: error.message });
      return null;
    }
  }

  /**
   * Interpret trend strength
   */
  _interpretTrendStrength(slope, rSquared) {
    if (rSquared < 0.3) return 'weak';
    if (rSquared < 0.7) return 'moderate';
    return 'strong';
  }

  /**
   * Generate regression recommendation
   */
  _generateRegressionRecommendation(category, change) {
    const severity = Math.abs(change);
    
    if (severity > 0.2) {
      return `Critical regression in ${category} performance. Immediate investigation required.`;
    } else if (severity > 0.1) {
      return `Significant regression in ${category} performance. Review recent changes.`;
    } else {
      return `Minor regression in ${category} performance. Monitor closely.`;
    }
  }

  /**
   * Perform pairwise significance tests
   */
  _performPairwiseTests(groupedResults, category) {
    const memoryTypes = Object.keys(groupedResults);
    const tests = {};

    for (let i = 0; i < memoryTypes.length; i++) {
      for (let j = i + 1; j < memoryTypes.length; j++) {
        const type1 = memoryTypes[i];
        const type2 = memoryTypes[j];
        
        const group1 = groupedResults[type1].map(r => r.scores?.[category] || 0);
        const group2 = groupedResults[type2].map(r => r.scores?.[category] || 0);
        
        const testKey = `${type1}_vs_${type2}`;
        tests[testKey] = this._testSignificance(group1, group2);
      }
    }

    return tests;
  }

  /**
   * Generate comparative recommendations
   */
  _generateComparativeRecommendations(comparison) {
    const recommendations = [];
    const overallRankings = comparison.rankings.overall || [];

    if (overallRankings.length > 1) {
      const best = overallRankings[0];
      const worst = overallRankings[overallRankings.length - 1];
      
      recommendations.push({
        type: 'performance_ranking',
        priority: 'high',
        message: `${best.memoryType} shows best overall performance (${best.mean.toFixed(1)}%)`,
        details: `Consider using ${best.memoryType} for production workloads.`
      });

      if (best.mean - worst.mean > 20) {
        recommendations.push({
          type: 'performance_gap',
          priority: 'medium',
          message: `Large performance gap between memory types (${(best.mean - worst.mean).toFixed(1)}% difference)`,
          details: `Investigate why ${worst.memoryType} underperforms compared to ${best.memoryType}.`
        });
      }
    }

    return recommendations;
  }

  /**
   * Test for normality (simplified Shapiro-Wilk approximation)
   */
  _testNormality(testResults) {
    const scores = testResults.map(r => r.scores?.overall || 0);
    
    if (scores.length < 3) {
      return { insufficient_data: true };
    }

    const mean = this._calculateMean(scores);
    const stdDev = this._calculateStandardDeviation(scores);
    const skewness = this._calculateSkewness(scores);
    const kurtosis = this._calculateKurtosis(scores);

    // Simplified normality assessment
    const isNormal = Math.abs(skewness) < 1 && Math.abs(kurtosis) < 1;

    return {
      mean,
      standardDeviation: stdDev,
      skewness,
      kurtosis,
      isNormal,
      interpretation: isNormal 
        ? 'Data appears to be approximately normally distributed'
        : 'Data shows significant deviation from normal distribution'
    };
  }

  /**
   * Detect outliers using IQR method
   */
  _detectOutliers(testResults) {
    const scores = testResults.map(r => r.scores?.overall || 0);
    const quartiles = this._calculateQuartiles(scores);
    const iqr = quartiles.q3 - quartiles.q1;
    const lowerBound = quartiles.q1 - 1.5 * iqr;
    const upperBound = quartiles.q3 + 1.5 * iqr;

    const outliers = scores.filter(score => score < lowerBound || score > upperBound);
    const outlierIndices = scores
      .map((score, index) => ({ score, index }))
      .filter(({ score }) => score < lowerBound || score > upperBound)
      .map(({ index }) => index);

    return {
      outliers,
      outlierIndices,
      lowerBound,
      upperBound,
      count: outliers.length,
      percentage: ((outliers.length / scores.length) * 100).toFixed(1)
    };
  }

  /**
   * Test homogeneity of variance (simplified)
   */
  _testHomogeneity(testResults) {
    // Group by memory type
    const groups = {};
    for (const result of testResults) {
      const memoryType = result.metadata?.memoryType || result.memoryType || 'unknown';
      if (!groups[memoryType]) groups[memoryType] = [];
      groups[memoryType].push(result.scores?.overall || 0);
    }

    const memoryTypes = Object.keys(groups);
    if (memoryTypes.length < 2) {
      return { insufficient_data: true };
    }

    const variances = {};
    for (const [type, scores] of Object.entries(groups)) {
      variances[type] = this._calculateVariance(scores);
    }

    const maxVariance = Math.max(...Object.values(variances));
    const minVariance = Math.min(...Object.values(variances));
    const varianceRatio = maxVariance / minVariance;

    // Simplified test: ratio > 4 suggests heterogeneity
    const isHomogeneous = varianceRatio < 4;

    return {
      variances,
      varianceRatio,
      isHomogeneous,
      interpretation: isHomogeneous
        ? 'Variances appear to be homogeneous across groups'
        : 'Significant heterogeneity in variances detected'
    };
  }

  /**
   * Test independence (simplified autocorrelation check)
   */
  _testIndependence(testResults) {
    if (testResults.length < 10) {
      return { insufficient_data: true };
    }

    // Sort by timestamp
    const sortedResults = testResults.sort((a, b) => {
      const timeA = new Date(a.metadata?.timestamp || a.timestamp || 0);
      const timeB = new Date(b.metadata?.timestamp || b.timestamp || 0);
      return timeA - timeB;
    });

    const scores = sortedResults.map(r => r.scores?.overall || 0);
    
    // Calculate lag-1 autocorrelation
    const mean = this._calculateMean(scores);
    let numerator = 0;
    let denominator = 0;

    for (let i = 0; i < scores.length - 1; i++) {
      numerator += (scores[i] - mean) * (scores[i + 1] - mean);
    }

    for (let i = 0; i < scores.length; i++) {
      denominator += Math.pow(scores[i] - mean, 2);
    }

    const autocorrelation = numerator / denominator;
    const isIndependent = Math.abs(autocorrelation) < 0.3;

    return {
      autocorrelation,
      isIndependent,
      interpretation: isIndependent
        ? 'Test results appear to be independent'
        : 'Significant autocorrelation detected - results may not be independent'
    };
  }

  /**
   * Analyze performance metrics trends
   */
  _analyzePerformanceMetrics(testResults) {
    const metrics = {
      apiCalls: testResults.map(r => r.performanceMetrics?.api?.totalCalls || 0),
      responseTime: testResults.map(r => r.performanceMetrics?.api?.averageResponseTime || 0),
      memoryUsage: testResults.map(r => r.performanceMetrics?.memory?.peakUsage || 0)
    };

    const analysis = {};
    for (const [metric, values] of Object.entries(metrics)) {
      analysis[metric] = {
        mean: this._calculateMean(values),
        trend: this._calculateTrend(values),
        variability: this._calculateCoefficientOfVariation(values)
      };
    }

    return analysis;
  }

  /**
   * Analyze performance trends over time
   */
  _analyzePerformanceTrends(sortedResults) {
    const trends = {};
    const performanceMetrics = ['apiCalls', 'responseTime', 'memoryUsage'];

    for (const metric of performanceMetrics) {
      let values;
      switch (metric) {
        case 'apiCalls':
          values = sortedResults.map(r => r.performanceMetrics?.api?.totalCalls || 0);
          break;
        case 'responseTime':
          values = sortedResults.map(r => r.performanceMetrics?.api?.averageResponseTime || 0);
          break;
        case 'memoryUsage':
          values = sortedResults.map(r => r.performanceMetrics?.memory?.peakUsage || 0);
          break;
        default:
          values = [];
      }

      trends[metric] = this._calculateTrend(values);
    }

    return trends;
  }

  /**
   * Calculate timespan between first and last results
   */
  _calculateTimespan(sortedResults) {
    if (sortedResults.length < 2) return null;

    const startTimestamp = sortedResults[0].metadata?.timestamp || sortedResults[0].timestamp;
    const endTimestamp = sortedResults[sortedResults.length - 1].metadata?.timestamp || sortedResults[sortedResults.length - 1].timestamp;

    // If no valid timestamps, return null
    if (!startTimestamp || !endTimestamp) return null;

    const start = new Date(startTimestamp);
    const end = new Date(endTimestamp);

    // Check if dates are valid
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return null;

    return {
      start: start.toISOString(),
      end: end.toISOString(),
      duration: end - start,
      durationDays: Math.round((end - start) / (1000 * 60 * 60 * 24))
    };
  }

  /**
   * Detect seasonality patterns (simplified)
   */
  _detectSeasonality(sortedResults) {
    if (sortedResults.length < 7) {
      return { insufficient_data: true };
    }

    // Group by day of week
    const dayGroups = {};
    for (const result of sortedResults) {
      const date = new Date(result.metadata?.timestamp || result.timestamp);
      const dayOfWeek = date.getDay();
      
      if (!dayGroups[dayOfWeek]) dayGroups[dayOfWeek] = [];
      dayGroups[dayOfWeek].push(result.scores?.overall || 0);
    }

    const dayAverages = {};
    for (const [day, scores] of Object.entries(dayGroups)) {
      dayAverages[day] = this._calculateMean(scores);
    }

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const seasonality = Object.entries(dayAverages).map(([day, avg]) => ({
      day: dayNames[parseInt(day)],
      average: avg,
      sampleSize: dayGroups[day].length
    }));

    return {
      byDayOfWeek: seasonality,
      hasSeasonality: Object.values(dayAverages).some(avg => Math.abs(avg - this._calculateMean(Object.values(dayAverages))) > 5)
    };
  }

  /**
   * Calculate volatility (standard deviation of changes)
   */
  _calculateVolatility(sortedResults) {
    if (sortedResults.length < 2) return 0;

    const scores = sortedResults.map(r => r.scores?.overall || 0);
    const changes = [];

    for (let i = 1; i < scores.length; i++) {
      changes.push(scores[i] - scores[i - 1]);
    }

    return this._calculateStandardDeviation(changes);
  }

  /**
   * Analyze test frequency
   */
  _analyzeTestFrequency(historicalData) {
    if (historicalData.length < 2) return null;

    const intervals = [];
    for (let i = 1; i < historicalData.length; i++) {
      const prev = new Date(historicalData[i - 1].timestamp);
      const curr = new Date(historicalData[i].timestamp);
      intervals.push(curr - prev);
    }

    const avgInterval = this._calculateMean(intervals);
    const avgIntervalHours = avgInterval / (1000 * 60 * 60);

    return {
      averageInterval: avgInterval,
      averageIntervalHours: avgIntervalHours.toFixed(1),
      totalTests: historicalData.length,
      frequency: avgIntervalHours < 1 ? 'very_high' : 
                avgIntervalHours < 24 ? 'high' :
                avgIntervalHours < 168 ? 'moderate' : 'low'
    };
  }

  /**
   * Identify patterns in historical data
   */
  _identifyPatterns(historicalData) {
    const patterns = [];

    // Check for improvement patterns
    const recentScores = historicalData.slice(-5).map(r => r.scores.overall);
    const olderScores = historicalData.slice(0, 5).map(r => r.scores.overall);

    if (recentScores.length >= 3 && olderScores.length >= 3) {
      const recentAvg = this._calculateMean(recentScores);
      const olderAvg = this._calculateMean(olderScores);

      if (recentAvg > olderAvg + 5) {
        patterns.push({
          type: 'improvement',
          description: 'Performance has improved over time',
          confidence: 'medium'
        });
      } else if (recentAvg < olderAvg - 5) {
        patterns.push({
          type: 'degradation',
          description: 'Performance has degraded over time',
          confidence: 'medium'
        });
      }
    }

    // Check for cyclical patterns
    if (historicalData.length >= 10) {
      const scores = historicalData.map(r => r.scores.overall);
      const volatility = this._calculateVolatility(historicalData);
      
      if (volatility > 10) {
        patterns.push({
          type: 'high_volatility',
          description: 'High variability in test results',
          confidence: 'high'
        });
      }
    }

    return patterns;
  }
}

module.exports = {
  StatisticalAnalyzer
};