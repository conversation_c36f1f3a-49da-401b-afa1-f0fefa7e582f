/**
 * Conversation Simulator - LLM Conversation Orchestration Engine
 *
 * This class orchestrates realistic conversations between user and assistant LL<PERSON>
 * while systematically injecting test facts to evaluate memory retention. It manages
 * the complex flow of alternating responses, fact injection timing, memory context
 * management, and conversation quality control.
 *
 * Key Features:
 * - Alternating user/assistant conversation flow
 * - Strategic fact injection at configurable intervals
 * - Memory context integration for both models
 * - Response quality validation and correction
 * - Echo detection and prevention
 * - Comprehensive conversation logging
 * - Performance monitoring and statistics
 * - Verbose debugging support
 *
 * The simulator ensures natural conversation flow while maintaining the experimental
 * rigor needed for memory testing. It handles edge cases like inappropriate responses,
 * model confusion, and maintains conversation coherence throughout the testing process.
 *
 * @class ConversationSimulator
 * 
 * @example
 * // Basic usage
 * const memory = MemoryFactory.createMemory('simple', 'session-123');
 * const simulator = new ConversationSimulator(memory, 'session-123', config);
 * const result = await simulator.runConversation(facts, 3);
 * 
 * @example
 * // With custom configuration
 * const config = new ConfigManager();
 * const simulator = new ConversationSimulator(memory, sessionId, config);
 * const { conversationLog, usedFacts } = await simulator.runConversation(testFacts, 5);
 * 
 * @example
 * // Access conversation details
 * console.log('Total messages:', simulator.conversationLog.length);
 * console.log('Memory contexts:', simulator.memoryContexts);
 */

const LLMModel = require('../models/llmModel');
const { prompts } = require('./prompts');
const { ConfigManager } = require('../config');
const { defaultLogger } = require('./logger');
const { DataIntegrityChecker } = require('../validation/dataIntegrityChecker');

class ConversationSimulator {
  /**
   * Creates a new ConversationSimulator instance
   * 
   * Initializes the conversation simulator with memory system integration,
   * LLM model instances for user and assistant roles, and configuration
   * management. Sets up logging and conversation tracking infrastructure.
   * 
   * @param {MemoryInterface} memory - Memory implementation instance for conversation storage
   * @param {string} sessionId - Unique session identifier for tracking and logging
   * @param {ConfigManager} [configManager] - Optional configuration manager instance
   * @throws {Error} Throws if memory instance is invalid
   * @throws {Error} Throws if sessionId is not provided
   * @throws {ConfigurationError} Throws if model configuration is invalid
   * 
   * @example
   * const memory = MemoryFactory.createMemory('simple', 'session-123');
   * const simulator = new ConversationSimulator(memory, 'session-123');
   * 
   * @example
   * // With custom configuration
   * const config = new ConfigManager();
   * const simulator = new ConversationSimulator(memory, sessionId, config);
   */
  constructor(memory, sessionId, configManager = null) {
    if (!memory) {
      throw new Error('Memory instance is required');
    }
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('Session ID must be a non-empty string');
    }

    this.memory = memory;
    this.sessionId = sessionId;
    this.config = configManager || new ConfigManager();
    
    this.userModel = new LLMModel(this.config.get('models.user.name'), this.config);
    this.assistantModel = new LLMModel(this.config.get('models.assistant.name'), this.config);
    this.conversationLog = [];
    this.memoryContexts = []; // Store memory contexts for verbose logging
    this.verboseLogging = this.config.isVerboseLogging();
    this.performanceTracker = null; // Will be set by main app
  }

  /**
   * Set performance tracker for integrated monitoring
   * 
   * @param {PerformanceTracker} tracker - Performance tracker instance
   */
  setPerformanceTracker(tracker) {
    this.performanceTracker = tracker;
    
    // Pass tracker to LLM models for API call tracking
    if (this.userModel && this.userModel.setPerformanceTracker) {
      this.userModel.setPerformanceTracker(tracker);
    }
    if (this.assistantModel && this.assistantModel.setPerformanceTracker) {
      this.assistantModel.setPerformanceTracker(tracker);
    }
  }

  /**
   * Execute a complete conversation simulation with systematic fact injection
   * 
   * This is the main orchestration method that manages the entire conversation flow.
   * It alternates between user and assistant responses while strategically injecting
   * test facts at specified intervals. The method ensures all facts are introduced
   * and allows for sufficient conversation after the last fact injection.
   * 
   * Conversation Flow:
   * 1. Initialize with first fact injection
   * 2. Generate assistant response to first fact
   * 3. Continue alternating user/assistant responses
   * 4. Inject new facts when message threshold is reached
   * 5. Continue until all facts are used and final messages are exchanged
   * 6. Return comprehensive conversation data
   * 
   * @param {Array<Object>} facts - Array of fact objects to inject during conversation
   * @param {string} facts[].fact - The fact statement to inject
   * @param {string} facts[].question - Question to test fact retention
   * @param {string} facts[].answer - Expected answer for evaluation
   * @param {string} [facts[].complexity] - Fact complexity level ('simple' or 'complex')
   * @param {number|string} [facts[].id] - Unique fact identifier
   * @param {number} messagesBetweenFacts - Number of conversation messages between fact injections
   * @returns {Promise<Object>} Comprehensive conversation results
   * @returns {Array<Object>} returns.conversationLog - Complete message history with roles and content
   * @returns {Array<Object>} returns.usedFacts - Facts that were successfully injected
   * @throws {Error} Throws if facts array is empty or invalid
   * @throws {Error} Throws if messagesBetweenFacts is not a positive number
   * @throws {APIError} Throws if LLM API calls fail repeatedly
   * @throws {MemoryError} Throws if memory operations fail
   * 
   * @example
   * const facts = [
   *   { fact: "Paris is the capital of France", question: "What is the capital of France?", answer: "Paris" },
   *   { fact: "The Earth orbits the Sun", question: "What does Earth orbit?", answer: "The Sun" }
   * ];
   * const result = await simulator.runConversation(facts, 3);
   * console.log(`Conversation had ${result.conversationLog.length} messages`);
   * console.log(`Used ${result.usedFacts.length} out of ${facts.length} facts`);
   * 
   * @example
   * // With complex facts
   * const complexFacts = [
   *   { 
   *     fact: "Machine learning algorithms require large datasets for training",
   *     question: "What do ML algorithms need for training?",
   *     answer: "Large datasets",
   *     complexity: "complex"
   *   }
   * ];
   * const { conversationLog, usedFacts } = await simulator.runConversation(complexFacts, 5);
   */
  async runConversation(facts, messagesBetweenFacts) {
    const sessionLogger = defaultLogger.child({ 
      sessionId: this.sessionId,
      component: 'ConversationSimulator'
    });

    sessionLogger.info('Starting conversation simulation', {
      totalFacts: facts.length,
      messagesBetweenFacts,
      userModel: this.config.get('models.user.name'),
      assistantModel: this.config.get('models.assistant.name')
    });

    // Track which facts were actually used in the conversation
    const usedFacts = [];

    // Handle empty facts array
    if (facts.length === 0) {
      sessionLogger.info('No facts provided, returning empty conversation', {
        sessionId: this.sessionId
      });
      return {
        conversationLog: [],
        usedFacts: [],
        memoryContexts: this.memoryContexts
      };
    }

    // Initialize conversation with first fact
    const firstFact = facts[0];

    // Track first fact injection with performance monitoring
    let factInjectionId = null;
    if (this.performanceTracker) {
      factInjectionId = this.performanceTracker.startOperation('fact-injection', {
        type: 'conversation-operation',
        component: 'conversationSimulator',
        metadata: { factIndex: 1, totalFacts: facts.length, fact: firstFact.fact }
      });
    }

    await this.addUserMessage(firstFact.fact);
    usedFacts.push(firstFact);

    if (this.performanceTracker && factInjectionId) {
      this.performanceTracker.endOperation(factInjectionId, {
        success: true,
        metadata: { factId: firstFact.id, injected: true }
      });
    }

    let factIndex = 1;
    let messageCount = 0;

    // Generate first assistant response
    await this.generateAssistantResponse();

    // Calculate total expected operations for progress tracking
    const expectedMessages = facts.length + (facts.length * messagesBetweenFacts) + messagesBetweenFacts;
    let currentMessage = 2; // Already have first fact and first assistant response

    // Continue conversation until all facts are used and we've had the required messages after the last fact
    while (true) {
      // If we've used all facts and had enough messages after, break
      if (factIndex >= facts.length && messageCount >= messagesBetweenFacts) {
        break;
      }

      // Display progress indicator
      const progressPercent = Math.round((currentMessage / expectedMessages) * 100);
      const progressBar = this.createProgressBar(progressPercent);
      
      // Check if we should inject a new fact
      if (factIndex < facts.length && messageCount >= messagesBetweenFacts) {
        // Display progress counter (facts remaining)
        console.log(`${progressBar} Injecting fact ${factIndex + 1}/${facts.length} (${facts.length - factIndex - 1} remaining)`);
        
        sessionLogger.info('Injecting fact into conversation', {
          factIndex: factIndex + 1,
          totalFacts: facts.length,
          factsRemaining: facts.length - factIndex - 1,
          messageCount,
          progress: `${progressPercent}%`
        });

        // Track fact injection with performance monitoring
        if (this.performanceTracker) {
          factInjectionId = this.performanceTracker.startOperation('fact-injection', {
            type: 'conversation-operation',
            component: 'conversationSimulator',
            metadata: { 
              factIndex: factIndex + 1, 
              totalFacts: facts.length, 
              fact: facts[factIndex].fact,
              progress: progressPercent
            }
          });
        }

        // Inject the next fact
        await this.addUserMessage(facts[factIndex].fact);
        usedFacts.push(facts[factIndex]);
        
        if (this.performanceTracker && factInjectionId) {
          this.performanceTracker.endOperation(factInjectionId, {
            success: true,
            metadata: { factId: facts[factIndex].id, injected: true }
          });
        }

        factIndex++;
        messageCount = 0;
        currentMessage++;

        // Generate assistant response after each fact
        await this.generateAssistantResponse();
        currentMessage++;
      } else {
        // Display progress for regular conversation
        console.log(`${progressBar} Conversation in progress (${currentMessage}/${expectedMessages} messages)`);
        
        // Generate user response
        await this.generateUserResponse();
        messageCount++;
        currentMessage++;

        // Generate assistant response after each user message
        await this.generateAssistantResponse();
        currentMessage++;
      }
    }

    console.log(`✓ Conversation completed (${this.conversationLog.length} total messages)`);

    sessionLogger.info('Conversation simulation completed', {
      totalMessages: this.conversationLog.length,
      factsUsed: usedFacts.length,
      totalFacts: facts.length,
      factUtilization: `${((usedFacts.length / facts.length) * 100).toFixed(1)}%`
    });

    // Perform conversation log integrity validation
    const integrityChecker = new DataIntegrityChecker();
    const expectedFlow = {
      expectedMessages: this.conversationLog.length,
      factsInjected: usedFacts.length
    };

    sessionLogger.debug('Validating conversation log integrity', {
      totalMessages: this.conversationLog.length,
      expectedFlow
    });

    const integrityResult = await integrityChecker.validateConversationLogIntegrity(
      this.conversationLog
    );

    if (!integrityResult.isValid) {
      sessionLogger.error('Conversation log failed integrity validation', {
        issues: integrityResult.issues,
        statistics: integrityResult.statistics
      });
      // Log as warning rather than throwing error to allow test to continue
      console.warn('⚠ Conversation log integrity issues detected:', integrityResult.issues.join(', '));
    }

    // Log warnings if any
    if (integrityResult.warnings && integrityResult.warnings.length > 0) {
      sessionLogger.warn('Conversation log integrity warnings', {
        warnings: integrityResult.warnings,
        statistics: integrityResult.statistics
      });
    }

    // Log conversation statistics
    if (integrityResult.statistics) {
      sessionLogger.info('Conversation statistics', integrityResult.statistics);
    }

    return {
      conversationLog: this.conversationLog,
      usedFacts: usedFacts
    };
  }

  /**
   * Create a visual progress bar for console output
   * @param {number} percent - Progress percentage (0-100)
   * @returns {string} Visual progress bar
   */
  createProgressBar(percent) {
    const width = 20;
    const filled = Math.floor((percent / 100) * width);
    const empty = Math.max(0, width - filled); // Ensure empty is never negative
    return `[${('█').repeat(filled)}${('░').repeat(empty)}] ${percent.toString().padStart(3)}%`;
  }

  /**
   * Add a user message to the conversation and memory system
   * 
   * This method adds a user message to both the conversation log and the memory
   * system. It handles memory context storage for verbose logging and provides
   * appropriate logging based on the configured verbosity level.
   * 
   * @param {string} content - The message content to add
   * @returns {Promise<void>} Promise that resolves when message is stored
   * @throws {Error} Throws if content is empty or invalid
   * @throws {MemoryError} Throws if memory storage fails
   * 
   * @example
   * await simulator.addUserMessage("Hello, how are you today?");
   * 
   * @example
   * // Adding a fact as user message
   * await simulator.addUserMessage("Did you know that Paris is the capital of France?");
   */
  async addUserMessage(content) {
    const message = { role: 'user', content };
    this.conversationLog.push(message);
    await this.memory.addMessage(message);

    // Store memory context if verbose logging is enabled
    if (this.verboseLogging) {
      const memoryContext = await this.memory.getMemoryContext();
      this.memoryContexts.push(memoryContext);
      defaultLogger.debug('User message added', {
        sessionId: this.sessionId,
        content,
        memoryContext
      });
    } else {
      defaultLogger.info('User message added', {
        sessionId: this.sessionId,
        content: content.length > 100 ? content.substring(0, 100) + '...' : content
      });
    }
  }

  /**
   * Generate and add an assistant response with memory context integration
   * 
   * This method generates a contextually appropriate assistant response by integrating
   * memory context with the conversation flow. It uses the memory system to provide
   * relevant conversation history and maintains conversation coherence.
   * 
   * @returns {Promise<string>} The generated assistant response
   * @throws {APIError} Throws if LLM API call fails
   * @throws {MemoryError} Throws if memory context retrieval fails
   * 
   * @example
   * const response = await simulator.generateAssistantResponse();
   * console.log('Assistant said:', response);
   */
  async generateAssistantResponse() {
    // Get memory context from the memory system
    const memoryContext = await this.memory.getMemoryContext();

    // Create an array of messages for the API call
    const messages = [
      { role: 'system', content: prompts.ASSISTANT_PROMPT },
      { role: 'system', content: `Conversation history:\n${memoryContext}` }
    ];

    // Add the most recent message if available (for immediate context)
    if (this.conversationLog.length > 0) {
      const lastMessage = this.conversationLog[this.conversationLog.length - 1];
      messages.push({ role: lastMessage.role, content: lastMessage.content });
    }

    // Generate the response with the full message history
    const response = await this.assistantModel.generateResponse(messages);

    // Ensure response is a string
    const responseText = response || '';

    const message = { role: 'assistant', content: responseText };
    this.conversationLog.push(message);
    await this.memory.addMessage(message);

    // Store memory context if verbose logging is enabled
    if (this.verboseLogging) {
      const updatedMemoryContext = await this.memory.getMemoryContext();
      this.memoryContexts.push(updatedMemoryContext);
      defaultLogger.debug('Assistant response generated', {
        sessionId: this.sessionId,
        response: responseText,
        memoryContext: updatedMemoryContext
      });
    } else {
      defaultLogger.info('Assistant response generated', {
        sessionId: this.sessionId,
        response: responseText.length > 100 ? responseText.substring(0, 100) + '...' : responseText
      });
    }

    return responseText;
  }

  /**
   * Generate and add a user response with quality validation and correction
   * 
   * This method generates contextually appropriate user responses while maintaining
   * conversation quality through echo detection, assistant-like response prevention,
   * and fallback mechanisms. It ensures the user model behaves as a human participant
   * rather than mimicking assistant behavior.
   * 
   * Quality Control Features:
   * - Echo detection and prevention
   * - Assistant-like response filtering
   * - Multiple retry attempts with corrective prompting
   * - Fallback responses for persistent issues
   * - Comprehensive logging of correction attempts
   * 
   * @returns {Promise<string>} The generated and validated user response
   * @throws {APIError} Throws if LLM API calls fail repeatedly
   * @throws {MemoryError} Throws if memory context retrieval fails
   * 
   * @example
   * const userResponse = await simulator.generateUserResponse();
   * console.log('User said:', userResponse);
   */
  async generateUserResponse() {
    // Get memory context from the memory system
    const memoryContext = await this.memory.getMemoryContext();

    // Create an array of messages for the API call with a very explicit system prompt
    const systemPrompt = prompts.USER_PROMPT;

    // Create messages array with system prompt and memory context
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'system', content: `Conversation history:\n${memoryContext}` }
    ];

    // Add the most recent message if available (for immediate context)
    let lastMessageContent = '';
    if (this.conversationLog.length > 0) {
      const lastMessage = this.conversationLog[this.conversationLog.length - 1];
      lastMessageContent = lastMessage.content;
      messages.push({
        role: 'system',
        content: `The AI assistant's last message was: "${lastMessage.content}"`
      });
    }

    // Add a final instruction to ensure the model responds as the user
    messages.push({
      role: 'system',
      content:
        'IMPORTANT: You are the human user named John. Respond to the assistant\'s last message with a short (1-3 sentences) reply. DO NOT repeat the assistant\'s message. DO NOT prefix your response with anything like "User:" or "[AI Assistant said]:". Just write a direct response as if you are having a normal conversation.'
    });

    // Generate the response with the full message history
    let response = await this.userModel.generateResponse(messages);

    // Ensure response is a string
    response = response || '';

    // Check if the response is an echo or looks like an assistant response
    let attempts = 0;
    const maxAttempts = 3;

    while (
      (this.isEchoResponse(response, lastMessageContent) ||
        this.looksLikeAssistantResponse(response)) &&
      attempts < maxAttempts
    ) {
      attempts++;
      defaultLogger.warn('User model generated inappropriate response', {
        sessionId: this.sessionId,
        attempt: attempts,
        maxAttempts,
        response: response.substring(0, 100),
        reason: this.isEchoResponse(response, lastMessageContent) ? 'echo' : 'assistant-like'
      });

      // Try again with a more explicit prompt
      messages.push({
        role: 'system',
        content: `ERROR: Your last response was inappropriate. ${this.isEchoResponse(response, lastMessageContent) ? "You repeated the assistant's message." : 'You responded as if you were the assistant.'}

        You are JOHN, the HUMAN USER. Write a NEW, ORIGINAL response to the assistant's last message.

        DO NOT:
        - Repeat the assistant's message
        - Start with "User:" or any prefix
        - Offer to help anyone
        - Refer to yourself as AI

        DO:
        - Respond naturally as a human would
        - Keep it short (1-3 sentences)
        - Ask a question or share an opinion`
      });

      response = await this.userModel.generateResponse(messages);
      // Ensure response is a string
      response = response || '';
    }

    // If we still have an inappropriate response after max attempts, generate a simple fallback
    if (
      this.isEchoResponse(response, lastMessageContent) ||
      this.looksLikeAssistantResponse(response)
    ) {
      defaultLogger.warn('Failed to generate appropriate user response, using fallback', {
        sessionId: this.sessionId,
        maxAttempts,
        finalResponse: response.substring(0, 100)
      });

      // Use one of several simple fallback responses
      const fallbacks = [
        "That's interesting. Can you tell me more about that?",
        "I'm not sure I understand. Could you explain that differently?",
        "Thanks for the information. I've been wondering about something else though.",
        'I see. What do you think about that topic?',
        "Hmm, I hadn't thought about it that way before."
      ];

      response = fallbacks[Math.floor(Math.random() * fallbacks.length)];
    }

    const message = { role: 'user', content: response };
    this.conversationLog.push(message);
    await this.memory.addMessage(message);

    // Store memory context if verbose logging is enabled
    if (this.verboseLogging) {
      const updatedMemoryContext = await this.memory.getMemoryContext();
      this.memoryContexts.push(updatedMemoryContext);
      defaultLogger.debug('User response generated', {
        sessionId: this.sessionId,
        response,
        memoryContext: updatedMemoryContext
      });
    } else {
      defaultLogger.info('User response generated', {
        sessionId: this.sessionId,
        response: response.length > 100 ? response.substring(0, 100) + '...' : response
      });
    }

    return response;
  }

  /**
   * Check if a response looks like it came from an assistant
   * @param {string} response - The response to check
   * @returns {boolean} True if the response looks like it came from an assistant
   */
  looksLikeAssistantResponse(response) {
    // Handle undefined or null response
    if (!response || typeof response !== 'string') {
      return false;
    }

    const assistantPhrases = [
      'how can i assist you',
      'how can i help you',
      'how may i assist you',
      'how may i help you',
      'is there anything else you need',
      'is there anything i can help you with',
      "i'm here to help",
      "i'm an ai",
      "i'm an assistant",
      'as an ai',
      'as an assistant',
      'hello john',
      'hi john',
      'nice to meet you',
      'user:',
      '[user]:',
      'assistant:',
      '[assistant]:',
      'ai assistant',
      'ai:',
      '[ai]:'
    ];

    const lowerResponse = response.toLowerCase();
    return assistantPhrases.some(phrase => lowerResponse.includes(phrase));
  }

  /**
   * Check if a response is just echoing back the previous message
   * @param {string} response - The response to check
   * @param {string} previousMessage - The previous message to compare against
   * @returns {boolean} True if the response is echoing the previous message
   */
  isEchoResponse(response, previousMessage) {
    if (!previousMessage || !response) return false;

    // Clean up both strings for comparison
    const cleanResponse = response.toLowerCase().trim();
    const cleanPrevious = previousMessage.toLowerCase().trim();

    // Check for exact match
    if (cleanResponse === cleanPrevious) return true;

    // Check if response starts with common prefixes followed by the previous message
    const prefixes = [
      '[ai assistant said]: ',
      'ai assistant said: ',
      'assistant: ',
      'assistant said: ',
      '[assistant]: ',
      '[assistant said]: '
    ];

    for (const prefix of prefixes) {
      if (cleanResponse.startsWith(prefix.toLowerCase())) {
        const withoutPrefix = cleanResponse.substring(prefix.length).trim();

        // Check if the rest matches the previous message
        if (
          cleanPrevious.includes(withoutPrefix) ||
          withoutPrefix.includes(cleanPrevious)
        ) {
          return true;
        }
      }
    }

    // Check if a significant portion of the previous message is contained in the response
    // This helps catch partial echoes
    if (cleanPrevious.length > 20) {
      const significantChunk = cleanPrevious.substring(
        0,
        Math.floor(cleanPrevious.length * 0.7)
      );
      if (cleanResponse.includes(significantChunk)) {
        return true;
      }
    }

    return false;
  }
}

module.exports = ConversationSimulator;
