/**
 * Statistical Analysis Tests
 * 
 * Comprehensive test suite for the StatisticalAnalyzer class,
 * covering all statistical analysis functionality including
 * descriptive statistics, trend analysis, confidence intervals,
 * and regression detection.
 */

const { StatisticalAnalyzer } = require('../statisticalAnalysis');
const fs = require('fs').promises;
const path = require('path');

// Mock logger to avoid console output during tests
jest.mock('../logger', () => ({
  defaultLogger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
  }
}));

describe('StatisticalAnalyzer', () => {
  let analyzer;
  let sampleTestResults;

  beforeEach(() => {
    analyzer = new StatisticalAnalyzer();
    
    // Create sample test results for testing
    sampleTestResults = [
      {
        scores: { overall: 85, simple: 90, complex: 80 },
        metadata: { timestamp: '2024-01-01T10:00:00Z', memoryType: 'simple' },
        performanceMetrics: { api: { totalCalls: 10, averageResponseTime: 1500 }, memory: { peakUsage: 50000000 } }
      },
      {
        scores: { overall: 78, simple: 82, complex: 74 },
        metadata: { timestamp: '2024-01-02T10:00:00Z', memoryType: 'simple' },
        performanceMetrics: { api: { totalCalls: 12, averageResponseTime: 1600 }, memory: { peakUsage: 52000000 } }
      },
      {
        scores: { overall: 92, simple: 95, complex: 89 },
        metadata: { timestamp: '2024-01-03T10:00:00Z', memoryType: 'summary' },
        performanceMetrics: { api: { totalCalls: 8, averageResponseTime: 1400 }, memory: { peakUsage: 48000000 } }
      },
      {
        scores: { overall: 88, simple: 91, complex: 85 },
        metadata: { timestamp: '2024-01-04T10:00:00Z', memoryType: 'summary' },
        performanceMetrics: { api: { totalCalls: 9, averageResponseTime: 1450 }, memory: { peakUsage: 49000000 } }
      },
      {
        scores: { overall: 75, simple: 80, complex: 70 },
        metadata: { timestamp: '2024-01-05T10:00:00Z', memoryType: 'simple' },
        performanceMetrics: { api: { totalCalls: 15, averageResponseTime: 1700 }, memory: { peakUsage: 55000000 } }
      }
    ];
  });

  describe('Constructor', () => {
    test('should initialize with default options', () => {
      const analyzer = new StatisticalAnalyzer();
      expect(analyzer.options.confidenceLevel).toBe(0.95);
      expect(analyzer.options.significanceLevel).toBe(0.05);
      expect(analyzer.options.regressionThreshold).toBe(0.1);
    });

    test('should accept custom options', () => {
      const customOptions = {
        confidenceLevel: 0.99,
        significanceLevel: 0.01,
        regressionThreshold: 0.05
      };
      const analyzer = new StatisticalAnalyzer(customOptions);
      expect(analyzer.options.confidenceLevel).toBe(0.99);
      expect(analyzer.options.significanceLevel).toBe(0.01);
      expect(analyzer.options.regressionThreshold).toBe(0.05);
    });
  });

  describe('analyzeTestResults', () => {
    test('should throw error for empty test results', async () => {
      await expect(analyzer.analyzeTestResults([])).rejects.toThrow('Test results must be a non-empty array');
    });

    test('should throw error for non-array input', async () => {
      await expect(analyzer.analyzeTestResults(null)).rejects.toThrow('Test results must be a non-empty array');
    });

    test('should return comprehensive analysis for valid results', async () => {
      const analysis = await analyzer.analyzeTestResults(sampleTestResults);
      
      expect(analysis).toHaveProperty('metadata');
      expect(analysis).toHaveProperty('descriptive');
      expect(analysis).toHaveProperty('confidence');
      expect(analysis).toHaveProperty('trends');
      expect(analysis).toHaveProperty('regression');
      expect(analysis).toHaveProperty('comparative');
      expect(analysis).toHaveProperty('significance');
      expect(analysis).toHaveProperty('recommendations');
      
      expect(analysis.metadata.sampleSize).toBe(5);
      expect(analysis.metadata.analysisTimestamp).toBeDefined();
    });
  });

  describe('calculateDescriptiveStatistics', () => {
    test('should calculate correct descriptive statistics', () => {
      const stats = analyzer.calculateDescriptiveStatistics(sampleTestResults);
      
      expect(stats.overall).toBeDefined();
      expect(stats.simple).toBeDefined();
      expect(stats.complex).toBeDefined();
      
      // Test overall scores: [85, 78, 92, 88, 75]
      expect(stats.overall.count).toBe(5);
      expect(stats.overall.mean).toBeCloseTo(83.6, 1);
      expect(stats.overall.median).toBe(85);
      expect(stats.overall.min).toBe(75);
      expect(stats.overall.max).toBe(92);
      expect(stats.overall.range).toBe(17);
      expect(stats.overall.standardDeviation).toBeGreaterThan(0);
      expect(stats.overall.variance).toBeGreaterThan(0);
    });

    test('should handle empty scores gracefully', () => {
      const emptyResults = [
        { scores: {}, metadata: { timestamp: '2024-01-01T10:00:00Z' } }
      ];
      
      const stats = analyzer.calculateDescriptiveStatistics(emptyResults);
      expect(stats.overall.mean).toBe(0);
      expect(stats.overall.count).toBe(1);
    });

    test('should calculate quartiles correctly', () => {
      const stats = analyzer.calculateDescriptiveStatistics(sampleTestResults);
      
      expect(stats.overall.quartiles).toBeDefined();
      expect(stats.overall.quartiles.q1).toBeDefined();
      expect(stats.overall.quartiles.q2).toBeDefined(); // median
      expect(stats.overall.quartiles.q3).toBeDefined();
    });

    test('should calculate skewness and kurtosis', () => {
      const stats = analyzer.calculateDescriptiveStatistics(sampleTestResults);
      
      expect(typeof stats.overall.skewness).toBe('number');
      expect(typeof stats.overall.kurtosis).toBe('number');
      expect(typeof stats.overall.coefficientOfVariation).toBe('number');
    });
  });

  describe('calculateConfidenceIntervals', () => {
    test('should calculate confidence intervals for all categories', () => {
      const intervals = analyzer.calculateConfidenceIntervals(sampleTestResults);
      
      expect(intervals.overall).toBeDefined();
      expect(intervals.simple).toBeDefined();
      expect(intervals.complex).toBeDefined();
      
      expect(intervals.overall.mean).toBeCloseTo(83.6, 1);
      expect(intervals.overall.lower).toBeLessThan(intervals.overall.mean);
      expect(intervals.overall.upper).toBeGreaterThan(intervals.overall.mean);
      expect(intervals.overall.confidenceLevel).toBe(0.95);
      expect(intervals.overall.interpretation).toBeDefined();
    });

    test('should use correct z-score for confidence level', () => {
      const analyzer99 = new StatisticalAnalyzer({ confidenceLevel: 0.99 });
      const intervals = analyzer99.calculateConfidenceIntervals(sampleTestResults);
      
      expect(intervals.overall.confidenceLevel).toBe(0.99);
      // 99% confidence interval should be wider than 95%
      const intervals95 = analyzer.calculateConfidenceIntervals(sampleTestResults);
      expect(intervals.overall.marginOfError).toBeGreaterThan(intervals95.overall.marginOfError);
    });
  });

  describe('analyzeTrends', () => {
    test('should return insufficient data message for small samples', () => {
      const smallSample = sampleTestResults.slice(0, 2);
      const trends = analyzer.analyzeTrends(smallSample);
      
      expect(trends.insufficient_data).toBe(true);
      expect(trends.message).toContain('At least 3 data points required');
    });

    test('should calculate trends for sufficient data', () => {
      const trends = analyzer.analyzeTrends(sampleTestResults);
      
      expect(trends.overall).toBeDefined();
      expect(trends.simple).toBeDefined();
      expect(trends.complex).toBeDefined();
      expect(trends.timespan).toBeDefined();
      expect(trends.seasonality).toBeDefined();
      expect(trends.volatility).toBeDefined();
      
      expect(trends.overall.slope).toBeDefined();
      expect(trends.overall.direction).toMatch(/improving|declining|stable/);
      expect(trends.overall.rSquared).toBeDefined();
      expect(trends.overall.strength).toMatch(/weak|moderate|strong/);
    });

    test('should sort results by timestamp', () => {
      // Create unsorted results
      const unsortedResults = [...sampleTestResults].reverse();
      const trends = analyzer.analyzeTrends(unsortedResults);
      
      // Should still calculate trends correctly
      expect(trends.overall).toBeDefined();
      expect(trends.timespan).toBeDefined();
    });

    test('should include performance trends when available', () => {
      const trends = analyzer.analyzeTrends(sampleTestResults);
      
      expect(trends.performance).toBeDefined();
      expect(trends.performance.apiCalls).toBeDefined();
      expect(trends.performance.responseTime).toBeDefined();
      expect(trends.performance.memoryUsage).toBeDefined();
    });
  });

  describe('detectPerformanceRegression', () => {
    test('should return insufficient data message for small samples', () => {
      const smallSample = sampleTestResults.slice(0, 3);
      const regression = analyzer.detectPerformanceRegression(smallSample);
      
      expect(regression.insufficient_data).toBe(true);
      expect(regression.message).toContain('At least 5 data points required');
    });

    test('should detect regression with sufficient data', () => {
      // Create data with clear regression
      const regressionData = [
        ...sampleTestResults,
        { scores: { overall: 60, simple: 65, complex: 55 }, metadata: { timestamp: '2024-01-06T10:00:00Z' } },
        { scores: { overall: 55, simple: 60, complex: 50 }, metadata: { timestamp: '2024-01-07T10:00:00Z' } }
      ];
      
      const regression = analyzer.detectPerformanceRegression(regressionData);
      
      expect(regression.baseline).toBeDefined();
      expect(regression.recent).toBeDefined();
      expect(regression.detection).toBeDefined();
      expect(regression.alerts).toBeDefined();
      
      expect(regression.detection.overall).toBeDefined();
      expect(regression.detection.overall.baselineMean).toBeDefined();
      expect(regression.detection.overall.recentMean).toBeDefined();
      expect(regression.detection.overall.percentageChange).toBeDefined();
    });

    test('should generate alerts for significant regressions', () => {
      // Create data with significant regression
      const regressionData = [
        { scores: { overall: 90 }, metadata: { timestamp: '2024-01-01T10:00:00Z' } },
        { scores: { overall: 88 }, metadata: { timestamp: '2024-01-02T10:00:00Z' } },
        { scores: { overall: 85 }, metadata: { timestamp: '2024-01-03T10:00:00Z' } },
        { scores: { overall: 50 }, metadata: { timestamp: '2024-01-04T10:00:00Z' } },
        { scores: { overall: 45 }, metadata: { timestamp: '2024-01-05T10:00:00Z' } }
      ];
      
      const regression = analyzer.detectPerformanceRegression(regressionData);
      
      expect(regression.alerts.length).toBeGreaterThan(0);
      const regressionAlert = regression.alerts.find(alert => alert.type === 'regression');
      expect(regressionAlert).toBeDefined();
      expect(regressionAlert.severity).toMatch(/high|medium/);
    });

    test('should detect improvements', () => {
      // Create data with improvement
      const improvementData = [
        { scores: { overall: 50 }, metadata: { timestamp: '2024-01-01T10:00:00Z' } },
        { scores: { overall: 52 }, metadata: { timestamp: '2024-01-02T10:00:00Z' } },
        { scores: { overall: 55 }, metadata: { timestamp: '2024-01-03T10:00:00Z' } },
        { scores: { overall: 85 }, metadata: { timestamp: '2024-01-04T10:00:00Z' } },
        { scores: { overall: 90 }, metadata: { timestamp: '2024-01-05T10:00:00Z' } }
      ];
      
      const regression = analyzer.detectPerformanceRegression(improvementData);
      
      const improvementAlert = regression.alerts.find(alert => alert.type === 'improvement');
      expect(improvementAlert).toBeDefined();
    });
  });

  describe('performComparativeAnalysis', () => {
    test('should return insufficient data message for single memory type', () => {
      const singleTypeResults = sampleTestResults.filter(r => r.metadata.memoryType === 'simple');
      const comparison = analyzer.performComparativeAnalysis(singleTypeResults);
      
      expect(comparison.insufficient_data).toBe(true);
      expect(comparison.message).toContain('At least 2 different memory types required');
    });

    test('should perform comparative analysis for multiple memory types', () => {
      const comparison = analyzer.performComparativeAnalysis(sampleTestResults);
      
      expect(comparison.memoryTypes).toContain('simple');
      expect(comparison.memoryTypes).toContain('summary');
      expect(comparison.statistics).toBeDefined();
      expect(comparison.rankings).toBeDefined();
      expect(comparison.significance).toBeDefined();
      expect(comparison.recommendations).toBeDefined();
      
      expect(comparison.statistics.simple).toBeDefined();
      expect(comparison.statistics.summary).toBeDefined();
      expect(comparison.rankings.overall).toBeDefined();
    });

    test('should rank memory types by performance', () => {
      const comparison = analyzer.performComparativeAnalysis(sampleTestResults);
      
      const overallRankings = comparison.rankings.overall;
      expect(overallRankings).toBeInstanceOf(Array);
      expect(overallRankings.length).toBe(2); // simple and summary
      
      // Rankings should be sorted by mean score (descending)
      for (let i = 0; i < overallRankings.length - 1; i++) {
        expect(overallRankings[i].mean).toBeGreaterThanOrEqual(overallRankings[i + 1].mean);
      }
    });

    test('should perform pairwise significance tests', () => {
      const comparison = analyzer.performComparativeAnalysis(sampleTestResults);
      
      expect(comparison.significance.overall).toBeDefined();
      expect(comparison.significance.overall['simple_vs_summary']).toBeDefined();
      
      const pairwiseTest = comparison.significance.overall['simple_vs_summary'];
      expect(pairwiseTest.tStatistic).toBeDefined();
      expect(pairwiseTest.pValue).toBeDefined();
      expect(pairwiseTest.isSignificant).toBeDefined();
    });
  });

  describe('performSignificanceTests', () => {
    test('should perform all significance tests', () => {
      const tests = analyzer.performSignificanceTests(sampleTestResults);
      
      expect(tests.normality).toBeDefined();
      expect(tests.outliers).toBeDefined();
      expect(tests.homogeneity).toBeDefined();
      expect(tests.independence).toBeDefined();
    });

    test('should test normality', () => {
      const tests = analyzer.performSignificanceTests(sampleTestResults);
      
      expect(tests.normality.mean).toBeDefined();
      expect(tests.normality.standardDeviation).toBeDefined();
      expect(tests.normality.skewness).toBeDefined();
      expect(tests.normality.kurtosis).toBeDefined();
      expect(typeof tests.normality.isNormal).toBe('boolean');
      expect(tests.normality.interpretation).toBeDefined();
    });

    test('should detect outliers', () => {
      // Add an outlier
      const dataWithOutlier = [
        ...sampleTestResults,
        { scores: { overall: 10 }, metadata: { timestamp: '2024-01-06T10:00:00Z' } }
      ];
      
      const tests = analyzer.performSignificanceTests(dataWithOutlier);
      
      expect(tests.outliers.outliers).toBeDefined();
      expect(tests.outliers.count).toBeGreaterThan(0);
      expect(tests.outliers.lowerBound).toBeDefined();
      expect(tests.outliers.upperBound).toBeDefined();
    });
  });

  describe('generateRecommendations', () => {
    test('should generate recommendations based on analysis', () => {
      const recommendations = analyzer.generateRecommendations(sampleTestResults);
      
      expect(recommendations).toBeInstanceOf(Array);
      
      // Check for sample size recommendation (should exist for small samples)
      const sampleSizeRec = recommendations.find(r => r.type === 'sample_size');
      expect(sampleSizeRec).toBeDefined();
      expect(sampleSizeRec.priority).toBe('high');
    });

    test('should recommend increasing sample size for small samples', () => {
      const smallSample = sampleTestResults.slice(0, 3);
      const recommendations = analyzer.generateRecommendations(smallSample);
      
      const sampleSizeRec = recommendations.find(r => r.type === 'sample_size');
      expect(sampleSizeRec).toBeDefined();
      expect(sampleSizeRec.message).toContain('Increase sample size');
    });

    test('should detect high variability', () => {
      // Create data with high variability
      const highVariabilityData = [
        { scores: { overall: 10 }, metadata: { timestamp: '2024-01-01T10:00:00Z' } },
        { scores: { overall: 90 }, metadata: { timestamp: '2024-01-02T10:00:00Z' } },
        { scores: { overall: 20 }, metadata: { timestamp: '2024-01-03T10:00:00Z' } },
        { scores: { overall: 80 }, metadata: { timestamp: '2024-01-04T10:00:00Z' } },
        { scores: { overall: 30 }, metadata: { timestamp: '2024-01-05T10:00:00Z' } }
      ];
      
      const recommendations = analyzer.generateRecommendations(highVariabilityData);
      
      const variabilityRec = recommendations.find(r => r.type === 'variability');
      expect(variabilityRec).toBeDefined();
    });

    test('should detect poor performance', () => {
      // Create data with poor performance
      const poorPerformanceData = sampleTestResults.map(r => ({
        ...r,
        scores: { overall: 30, simple: 35, complex: 25 }
      }));
      
      const recommendations = analyzer.generateRecommendations(poorPerformanceData);
      
      const performanceRec = recommendations.find(r => r.type === 'performance');
      expect(performanceRec).toBeDefined();
      expect(performanceRec.priority).toBe('high');
    });
  });

  describe('analyzeHistoricalResults', () => {
    let tempDir;
    
    beforeEach(async () => {
      // Create temporary directory for test files
      tempDir = path.join(__dirname, 'temp_test_results');
      await fs.mkdir(tempDir, { recursive: true });
    });

    afterEach(async () => {
      // Clean up temporary directory
      try {
        await fs.rmdir(tempDir, { recursive: true });
      } catch (error) {
        // Ignore cleanup errors
      }
    });

    test('should throw error for non-existent directory', async () => {
      await expect(analyzer.analyzeHistoricalResults('/non/existent/path')).rejects.toThrow();
    });

    test('should throw error for directory with no result files', async () => {
      await expect(analyzer.analyzeHistoricalResults(tempDir)).rejects.toThrow('No test result files found');
    });

    test('should analyze historical results from files', async () => {
      // Create sample result files
      const resultFile1 = `# LLM Memory Test Results

## Test Configuration
- **Session ID**: test-session-1
- **Memory Type**: simple
- **Test Scenario**: customer

## Test Results
- **Overall Score**: 85.00%
- **Simple Facts Score**: 90.00%
- **Complex Facts Score**: 80.00%`;

      const resultFile2 = `# LLM Memory Test Results

## Test Configuration
- **Session ID**: test-session-2
- **Memory Type**: summary
- **Test Scenario**: customer

## Test Results
- **Overall Score**: 78.00%
- **Simple Facts Score**: 82.00%
- **Complex Facts Score**: 74.00%`;

      await fs.writeFile(path.join(tempDir, 'results_1640995200_simple_customer_test1.md'), resultFile1);
      await fs.writeFile(path.join(tempDir, 'results_1640995300_summary_customer_test2.md'), resultFile2);

      const analysis = await analyzer.analyzeHistoricalResults(tempDir);
      
      expect(analysis.metadata.sampleSize).toBe(2);
      expect(analysis.descriptive).toBeDefined();
      expect(analysis.historical).toBeDefined();
      expect(analysis.historical.timespan).toBeDefined();
      expect(analysis.historical.frequency).toBeDefined();
      expect(analysis.historical.patterns).toBeDefined();
    });

    test('should handle malformed result files gracefully', async () => {
      // Create valid and invalid files
      const validFile = `# LLM Memory Test Results
- **Overall Score**: 85.00%
- **Simple Facts Score**: 90.00%
- **Complex Facts Score**: 80.00%`;

      const invalidFile = 'This is not a valid result file';

      await fs.writeFile(path.join(tempDir, 'results_1640995200_simple_customer_valid.md'), validFile);
      await fs.writeFile(path.join(tempDir, 'results_1640995300_simple_customer_invalid.md'), invalidFile);

      const analysis = await analyzer.analyzeHistoricalResults(tempDir);
      
      // Should analyze both files (invalid file will have default values)
      expect(analysis.metadata.sampleSize).toBe(2);
    });
  });

  describe('Private helper methods', () => {
    test('_calculateMean should calculate correct mean', () => {
      expect(analyzer._calculateMean([1, 2, 3, 4, 5])).toBe(3);
      expect(analyzer._calculateMean([])).toBe(0);
      expect(analyzer._calculateMean([10])).toBe(10);
    });

    test('_calculateMedian should calculate correct median', () => {
      expect(analyzer._calculateMedian([1, 2, 3, 4, 5])).toBe(3);
      expect(analyzer._calculateMedian([1, 2, 3, 4])).toBe(2.5);
      expect(analyzer._calculateMedian([])).toBe(0);
      expect(analyzer._calculateMedian([10])).toBe(10);
    });

    test('_calculateStandardDeviation should calculate correct standard deviation', () => {
      const values = [2, 4, 4, 4, 5, 5, 7, 9];
      const stdDev = analyzer._calculateStandardDeviation(values);
      expect(stdDev).toBeCloseTo(2, 0); // Approximately 2
      
      expect(analyzer._calculateStandardDeviation([])).toBe(0);
      expect(analyzer._calculateStandardDeviation([5, 5, 5])).toBe(0);
    });

    test('_calculateTrend should detect trends correctly', () => {
      // Increasing trend
      const increasingTrend = analyzer._calculateTrend([1, 2, 3, 4, 5]);
      expect(increasingTrend.slope).toBeGreaterThan(0);
      expect(increasingTrend.direction).toBe('improving');
      
      // Decreasing trend
      const decreasingTrend = analyzer._calculateTrend([5, 4, 3, 2, 1]);
      expect(decreasingTrend.slope).toBeLessThan(0);
      expect(decreasingTrend.direction).toBe('declining');
      
      // Stable trend
      const stableTrend = analyzer._calculateTrend([3, 3, 3, 3, 3]);
      expect(Math.abs(stableTrend.slope)).toBeLessThan(0.1);
      expect(stableTrend.direction).toBe('stable');
    });

    test('_parseResultFile should parse result files correctly', () => {
      const content = `# LLM Memory Test Results

## Test Configuration
- **Session ID**: 12345678-1234-1234-1234-123456789abc
- **Memory Type**: simple
- **Test Scenario**: customer

## Test Results
- **Overall Score**: 85.50%
- **Simple Facts Score**: 90.25%
- **Complex Facts Score**: 80.75%`;

      const filename = 'results_1640995200_simple_customer_test.md';
      const parsed = analyzer._parseResultFile(content, filename);
      
      expect(parsed).toBeDefined();
      expect(parsed.sessionId).toBe('12345678-1234-1234-1234-123456789abc');
      expect(parsed.memoryType).toBe('simple');
      expect(parsed.scores.overall).toBe(85.5);
      expect(parsed.scores.simple).toBe(90.25);
      expect(parsed.scores.complex).toBe(80.75);
      expect(parsed.timestamp).toBeDefined();
    });

    test('_parseResultFile should handle malformed content', () => {
      const malformedContent = 'This is not a valid result file';
      const filename = 'invalid_file.md';
      
      const parsed = analyzer._parseResultFile(malformedContent, filename);
      expect(parsed).toBeDefined();
      expect(parsed.scores.overall).toBe(0);
      expect(parsed.memoryType).toBe('unknown');
    });
  });

  describe('Edge cases and error handling', () => {
    test('should handle results with missing scores', () => {
      const incompleteResults = [
        { metadata: { timestamp: '2024-01-01T10:00:00Z' } },
        { scores: { overall: 80 }, metadata: { timestamp: '2024-01-02T10:00:00Z' } }
      ];
      
      const stats = analyzer.calculateDescriptiveStatistics(incompleteResults);
      expect(stats.overall.count).toBe(2);
      expect(stats.overall.mean).toBe(40); // (0 + 80) / 2
    });

    test('should handle results with missing metadata', () => {
      const noMetadataResults = [
        { scores: { overall: 80 } },
        { scores: { overall: 85 } },
        { scores: { overall: 90 } }
      ];
      
      const trends = analyzer.analyzeTrends(noMetadataResults);
      expect(trends.overall).toBeDefined();
    });

    test('should handle single data point gracefully', () => {
      const singleResult = [sampleTestResults[0]];
      
      const stats = analyzer.calculateDescriptiveStatistics(singleResult);
      expect(stats.overall.count).toBe(1);
      expect(stats.overall.standardDeviation).toBe(0);
      
      const intervals = analyzer.calculateConfidenceIntervals(singleResult);
      expect(intervals.overall.marginOfError).toBe(0);
    });

    test('should handle identical values', () => {
      const identicalResults = Array(5).fill({
        scores: { overall: 80, simple: 80, complex: 80 },
        metadata: { timestamp: '2024-01-01T10:00:00Z', memoryType: 'simple' }
      });
      
      const stats = analyzer.calculateDescriptiveStatistics(identicalResults);
      expect(stats.overall.mean).toBe(80);
      expect(stats.overall.standardDeviation).toBe(0);
      expect(stats.overall.variance).toBe(0);
    });
  });
});