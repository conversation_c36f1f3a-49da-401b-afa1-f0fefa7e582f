/**
 * Evaluator - Memory Retention Assessment and Scoring Engine
 *
 * This class provides comprehensive evaluation capabilities for assessing memory
 * retention in LLM conversations. It systematically tests fact recall through
 * targeted questions, scores responses using LLM-based evaluation, and generates
 * detailed performance reports with statistical analysis.
 *
 * Key Features:
 * - Systematic fact recall testing through targeted questions
 * - LLM-based response evaluation and scoring (0-10 scale)
 * - Memory context integration for evaluation accuracy
 * - Complexity-based scoring (simple vs complex facts)
 * - Statistical analysis and performance metrics
 * - Comprehensive result reporting and visualization
 * - File-based result persistence with detailed formatting
 * - Progress tracking and performance monitoring
 *
 * The evaluator uses a separate LLM model to objectively assess whether responses
 * demonstrate proper fact retention, providing quantitative metrics for memory
 * system performance comparison and analysis.
 *
 * @class Evaluator
 * 
 * @example
 * // Basic usage
 * const evaluator = new Evaluator('session-123', config);
 * const results = await evaluator.evaluateMemory(memory, facts, conversationLog);
 * const score = evaluator.calculateOverallScore(results);
 * 
 * @example
 * // With result saving
 * const evaluator = new Evaluator(sessionId, config);
 * const results = await evaluator.evaluateMemory(memory, facts, log);
 * await evaluator.saveResultsToFile('results.md', {
 *   sessionId, overallScore: score, evaluationResults: results
 * });
 * 
 * @example
 * // Score calculation
 * const scores = evaluator.calculateOverallScore(results);
 * console.log(`Overall: ${scores.overall}%, Simple: ${scores.simple}%, Complex: ${scores.complex}%`);
 */

require('dotenv').config();
const fs = require('fs').promises;
const LLMModel = require('../models/llmModel');
const { prompts } = require('./prompts');
const { ConfigManager } = require('../config');
const { defaultLogger } = require('./logger');
const { DataIntegrityChecker } = require('../validation/dataIntegrityChecker');

class Evaluator {
  /**
   * Creates a new Evaluator instance for memory retention assessment
   * 
   * Initializes the evaluator with session tracking, configuration management,
   * and an LLM model specifically configured for objective evaluation tasks.
   * The evaluator model is separate from conversation models to ensure unbiased
   * assessment of memory retention performance.
   * 
   * @param {string} sessionId - Unique session identifier for tracking and logging
   * @param {ConfigManager} [configManager] - Optional configuration manager instance
   * @throws {Error} Throws if sessionId is not provided
   * @throws {ConfigurationError} Throws if evaluator model configuration is invalid
   * 
   * @example
   * const evaluator = new Evaluator('session-123');
   * 
   * @example
   * // With custom configuration
   * const config = new ConfigManager();
   * const evaluator = new Evaluator(sessionId, config);
   */
  constructor(sessionId, configManager = null) {
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('Session ID must be a non-empty string');
    }

    this.sessionId = sessionId;
    this.config = configManager || new ConfigManager();
    this.evaluatorModel = new LLMModel(this.config.get('models.evaluator.name'), this.config);
    this.performanceTracker = null; // Will be set by main app
  }

  /**
   * Set performance tracker for integrated monitoring
   * 
   * @param {PerformanceTracker} tracker - Performance tracker instance
   */
  setPerformanceTracker(tracker) {
    this.performanceTracker = tracker;
    
    // Pass tracker to LLM model for API call tracking
    if (this.evaluatorModel && this.evaluatorModel.setPerformanceTracker) {
      this.evaluatorModel.setPerformanceTracker(tracker);
    }
  }

  /**
   * Systematically evaluate memory retention through targeted fact recall testing
   * 
   * This method conducts comprehensive memory evaluation by asking specific questions
   * about facts that were introduced during the conversation. It integrates memory
   * context to provide relevant conversation history and uses LLM-based scoring
   * to assess response accuracy objectively.
   * 
   * Evaluation Process:
   * 1. Iterate through each fact that was introduced
   * 2. Retrieve relevant memory context for the fact's question
   * 3. Ask the evaluator model the fact-specific question
   * 4. Score the response against the expected answer
   * 5. Compile comprehensive results with context and metadata
   * 6. Handle errors gracefully with fallback scoring
   * 
   * @param {MemoryInterface} memory - Memory instance containing conversation history
   * @param {Array<Object>} facts - Array of fact objects that were introduced
   * @param {string} facts[].id - Unique fact identifier
   * @param {string} facts[].fact - The original fact statement
   * @param {string} facts[].question - Question to test fact retention
   * @param {string} facts[].answer - Expected answer for comparison
   * @param {string} [facts[].complexity] - Fact complexity ('simple' or 'complex')
   * @param {Array<Object>} conversationLog - Complete conversation message history
   * @returns {Promise<Array<Object>>} Comprehensive evaluation results
   * @returns {string} returns[].factId - Fact identifier
   * @returns {string} returns[].fact - Original fact statement
   * @returns {string} returns[].question - Question that was asked
   * @returns {string} returns[].expectedAnswer - Expected answer
   * @returns {string} returns[].actualResponse - LLM's actual response
   * @returns {number} returns[].score - Score from 0-10
   * @returns {string} returns[].complexity - Fact complexity level
   * @returns {string} returns[].memoryContext - Memory context used for evaluation
   * @throws {MemoryError} Throws if memory context retrieval fails
   * @throws {APIError} Throws if evaluator model calls fail
   * 
   * @example
   * const facts = [
   *   { id: 1, fact: "Paris is the capital of France", question: "What is the capital of France?", answer: "Paris" }
   * ];
   * const results = await evaluator.evaluateMemory(memory, facts, conversationLog);
   * console.log(`Fact ${results[0].factId} scored ${results[0].score}/10`);
   * 
   * @example
   * // Handle evaluation results
   * const results = await evaluator.evaluateMemory(memory, facts, log);
   * results.forEach(result => {
   *   console.log(`${result.complexity} fact: ${result.score}/10 - ${result.question}`);
   * });
   */
  async evaluateMemory(memory, facts, conversationLog) {
    const sessionLogger = defaultLogger.child({ 
      sessionId: this.sessionId,
      component: 'Evaluator'
    });

    sessionLogger.info('Starting memory evaluation', {
      totalFacts: facts.length,
      evaluatorModel: this.config.get('models.evaluator.name')
    });

    const results = [];

    // No need to check for verbose logging anymore as we always include memory context

    for (let i = 0; i < facts.length; i++) {
      const fact = facts[i];
      
      // Display progress indicator
      const progressPercent = Math.round(((i + 1) / facts.length) * 100);
      const progressBar = this.createProgressBar(progressPercent);
      console.log(`${progressBar} Evaluating fact ${i + 1}/${facts.length} (${facts.length - i - 1} remaining)`);
      
      sessionLogger.info('Evaluating fact', {
        factIndex: i + 1,
        totalFacts: facts.length,
        factsRemaining: facts.length - i - 1,
        factId: fact.id,
        complexity: fact.complexity,
        progress: `${progressPercent}%`
      });

      // Track individual fact evaluation with performance monitoring
      let factEvaluationId = null;
      if (this.performanceTracker) {
        factEvaluationId = this.performanceTracker.startOperation('fact-evaluation', {
          type: 'evaluation-operation',
          component: 'evaluator',
          metadata: { 
            factId: fact.id, 
            factIndex: i + 1, 
            totalFacts: facts.length,
            complexity: fact.complexity || 'simple',
            question: fact.question
          }
        });
      }

      try {
        // Get memory context with the current question to improve relevance
        const question = fact.question;
        sessionLogger.debug('Getting memory context for question', {
          factId: fact.id,
          question
        });

        // Track memory context retrieval
        let memoryContextId = null;
        if (this.performanceTracker) {
          memoryContextId = this.performanceTracker.startOperation('memory-context-retrieval', {
            type: 'memory-operation',
            component: 'memory',
            metadata: { factId: fact.id, question }
          });
        }

        const memoryContext = await memory.getMemoryContext(question, {
          includeRelevantHistory: true
        });

        if (this.performanceTracker && memoryContextId) {
          this.performanceTracker.endOperation(memoryContextId, {
            success: true,
            metadata: { contextLength: memoryContext.length }
          });
        }

        // Ask the question
        const systemPrompt = prompts.EVALUATOR_PROMPT;

        // Format the question to encourage a direct, factual answer
        const formattedQuestion = `Based on our conversation history ONLY, ${question} Please give a short, direct answer.`;

        // Track LLM response generation
        let responseGenerationId = null;
        if (this.performanceTracker) {
          responseGenerationId = this.performanceTracker.startOperation('llm-response-generation', {
            type: 'api-operation',
            component: 'evaluatorModel',
            metadata: { factId: fact.id, question }
          });
        }

        const response = await this.evaluatorModel.generateResponse(
          systemPrompt,
          [
            {
              role: 'system',
              content: `Conversation history:\n${memoryContext}`
            },
            { role: 'user', content: formattedQuestion }
          ]
        );

        if (this.performanceTracker && responseGenerationId) {
          this.performanceTracker.endOperation(responseGenerationId, {
            success: true,
            metadata: { responseLength: response.length }
          });
        }

        // Track response scoring
        let scoringId = null;
        if (this.performanceTracker) {
          scoringId = this.performanceTracker.startOperation('response-scoring', {
            type: 'evaluation-operation',
            component: 'evaluator',
            metadata: { factId: fact.id, expectedAnswer: fact.answer }
          });
        }

        // Evaluate the response
        const score = await this.scoreResponse(response, fact.answer);

        if (this.performanceTracker && scoringId) {
          this.performanceTracker.endOperation(scoringId, {
            success: true,
            metadata: { score, expectedAnswer: fact.answer, actualResponse: response }
          });
        }

        // Create result object with all the data
        const resultObj = {
          factId: fact.id,
          fact: fact.fact,
          question: question,
          expectedAnswer: fact.answer,
          actualResponse: response,
          score: score,
          complexity: fact.complexity || 'simple' // Default to simple if not specified
        };

        // Always add memory context to the result
        resultObj.memoryContext = memoryContext;

        results.push(resultObj);

        // End fact evaluation tracking
        if (this.performanceTracker && factEvaluationId) {
          this.performanceTracker.endOperation(factEvaluationId, {
            success: true,
            metadata: { 
              score, 
              complexity: fact.complexity || 'simple',
              responseLength: response.length,
              contextLength: memoryContext.length
            }
          });
        }

        sessionLogger.info('Fact evaluation completed', {
          factId: fact.id,
          question,
          expectedAnswer: fact.answer,
          actualResponse: response.length > 100 ? response.substring(0, 100) + '...' : response,
          score,
          complexity: fact.complexity || 'simple'
        });
      } catch (error) {
        // End fact evaluation tracking with error
        if (this.performanceTracker && factEvaluationId) {
          this.performanceTracker.endOperation(factEvaluationId, {
            success: false,
            error: error.message
          });
        }

        sessionLogger.error('Error evaluating fact', {
          factId: fact.id,
          error: error.message,
          stack: error.stack
        });
        results.push({
          factId: fact.id,
          fact: fact.fact,
          question: fact.question,
          expectedAnswer: fact.answer,
          actualResponse: 'Error: Could not evaluate',
          score: 0,
          complexity: fact.complexity || 'simple' // Default to simple if not specified
        });
      }
    }

    console.log(`✓ Evaluation completed (${results.length} facts evaluated)`);
    return results;
  }

  /**
   * Create a visual progress bar for console output
   * @param {number} percent - Progress percentage (0-100)
   * @returns {string} Visual progress bar
   */
  createProgressBar(percent) {
    const width = 20;
    const filled = Math.round((percent / 100) * width);
    const empty = width - filled;
    return `[${('█').repeat(filled)}${('░').repeat(empty)}] ${percent.toString().padStart(3)}%`;
  }

  /**
   * Score a response against expected answer using LLM-based evaluation
   * 
   * This method uses a separate LLM model to objectively score how well an actual
   * response matches the expected answer. The scoring is done on a 0-10 scale
   * with robust parsing and fallback mechanisms for reliable evaluation.
   * 
   * @param {string} response - The actual response to evaluate
   * @param {string} expectedAnswer - The expected/correct answer for comparison
   * @returns {Promise<number>} Numeric score from 0 to 10 (inclusive)
   * @throws {APIError} Throws if evaluator model call fails
   * 
   * @example
   * const score = await evaluator.scoreResponse("Paris", "Paris");
   * console.log(score); // Should be close to 10
   * 
   * @example
   * const score = await evaluator.scoreResponse("London", "Paris");
   * console.log(score); // Should be close to 0
   */
  async scoreResponse(response, expectedAnswer) {
    const systemPrompt = prompts.SCORING_PROMPT;

    const scoreResponse = await this.evaluatorModel.generateResponse(
      systemPrompt,
      [
        {
          role: 'user',
          content: `Expected answer: "${expectedAnswer}"\nActual response: "${response}"\n\nScore (0-10):`
        }
      ]
    );

    // Extract numeric score
    const scoreMatch = scoreResponse ? scoreResponse.match(/\d+/) : null;
    if (scoreMatch) {
      const score = parseInt(scoreMatch[0], 10);
      return Math.min(Math.max(score, 0), 10); // Ensure score is between 0 and 10
    }

    defaultLogger.warn('Could not parse score from evaluator response', {
      scoreResponse,
      expectedAnswer,
      actualResponse: response
    });
    return 0; // Default to 0 if parsing fails
  }

  /**
   * Calculate comprehensive performance scores from evaluation results
   * 
   * This method analyzes evaluation results to compute overall performance metrics
   * including separate scores for simple and complex facts. It provides detailed
   * statistical analysis with proper percentage calculations and rounding.
   * 
   * @param {Array<Object>} results - Array of evaluation result objects
   * @param {number} results[].score - Individual fact score (0-10)
   * @param {string} results[].complexity - Fact complexity ('simple' or 'complex')
   * @returns {Object} Comprehensive score breakdown
   * @returns {number} returns.overall - Overall score as percentage (0-100)
   * @returns {number} returns.simple - Simple facts score as percentage (0-100)
   * @returns {number} returns.complex - Complex facts score as percentage (0-100)
   * 
   * @example
   * const scores = evaluator.calculateOverallScore(evaluationResults);
   * console.log(`Overall: ${scores.overall}%`);
   * console.log(`Simple: ${scores.simple}%, Complex: ${scores.complex}%`);
   * 
   * @example
   * // Handle empty results
   * const scores = evaluator.calculateOverallScore([]);
   * console.log(scores); // { overall: 0, simple: 0, complex: 0 }
   */
  calculateOverallScore(results) {
    if (results.length === 0) return { overall: 0, simple: 0, complex: 0 };

    // Separate results by complexity
    const simpleResults = results.filter(
      result => result.complexity === 'simple'
    );
    const complexResults = results.filter(
      result => result.complexity === 'complex'
    );

    // Calculate scores for each category
    const calculateScore = resultsArray => {
      if (resultsArray.length === 0) return 0;
      const totalScore = resultsArray.reduce(
        (sum, result) => sum + result.score,
        0
      );
      const maxPossibleScore = resultsArray.length * 10;
      const percentage = (totalScore / maxPossibleScore) * 100;
      return Math.round(percentage * 100) / 100;
    };

    // Calculate the total score from all results
    const totalScore = results.reduce((sum, result) => sum + result.score, 0);

    // Calculate the maximum possible score (10 points per fact)
    const maxPossibleScore = results.length * 10;

    // Calculate the percentage with 2 decimal places
    const overallPercentage = (totalScore / maxPossibleScore) * 100;

    // Round to 2 decimal places
    const overall = Math.round(overallPercentage * 100) / 100;
    const simple = calculateScore(simpleResults);
    const complex = calculateScore(complexResults);

    return { overall, simple, complex };
  }

  /**
   * Create a visual bar chart for a percentage score
   * @param {number} percentage - Score as a percentage (0-100)
   * @returns {string} Visual bar chart using # and -
   */
  createPercentageBar(percentage) {
    // Ensure percentage is between 0 and 100
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    // Calculate how many filled characters to show (out of 10)
    const filledCount = Math.round(clampedPercentage / 10);
    const emptyCount = 10 - filledCount;

    // Create the bar with # for filled and - for empty
    const filledChars = '##'.repeat(filledCount);
    const emptyChars = '  '.repeat(emptyCount);

    return `[${filledChars}${emptyChars}]`;
  }

  /**
   * Format a percentage for display
   * @param {number} percentage - Score as a percentage (0-100)
   * @returns {string} Formatted percentage (e.g., "050%")
   */
  formatPercentage(percentage) {
    // Ensure percentage is between 0 and 100
    const clampedPercentage = Math.max(0, Math.min(100, percentage));

    // Format as 3-digit string with leading zeros
    return clampedPercentage.toFixed(0).padStart(3, '0') + '%';
  }

  /**
   * Save evaluation results to a file
   * @param {string} filePath - Path to save the results
   * @param {Object} data - Results data
   * @returns {Promise<void>}
   */
  async saveResultsToFile(filePath, data) {
    const {
      sessionId,
      memoryType,
      testFactsFile,
      testFactsCount,
      messagesBetweenFacts,
      userModel,
      assistantModel,
      evaluatorModel,
      summaryModel,
      knowledgeExtractionModel,
      overallScore,
      evaluationResults,
      conversationLog,
      memoryContexts,
      performanceMetrics,
      apiStats,
      memoryStats,
      systemStats,
      operationStats
    } = data;

    // Perform result output format validation before saving
    const integrityChecker = new DataIntegrityChecker();
    
    const sessionLogger = defaultLogger.child({ 
      sessionId: this.sessionId,
      component: 'Evaluator'
    });

    sessionLogger.debug('Validating result output format', {
      filePath,
      dataKeys: Object.keys(data)
    });

    const formatResult = integrityChecker.validateResultOutputFormat(data, filePath);
    
    if (!formatResult.isValid) {
      sessionLogger.error('Result output format validation failed', {
        filePath,
        issues: formatResult.issues
      });
      throw new Error(`Result output format validation failed: ${formatResult.issues.join(', ')}`);
    }

    // Log warnings if any
    if (formatResult.warnings && formatResult.warnings.length > 0) {
      sessionLogger.warn('Result output format warnings', {
        filePath,
        warnings: formatResult.warnings
      });
      console.warn('⚠ Result format warnings:', formatResult.warnings.join(', '));
    }

    // Log recommendations if any
    if (formatResult.recommendations && formatResult.recommendations.length > 0) {
      sessionLogger.info('Result output format recommendations', {
        filePath,
        recommendations: formatResult.recommendations
      });
    }

    sessionLogger.debug('Result output format validation completed successfully', {
      filePath,
      warningsCount: formatResult.warnings?.length || 0,
      recommendationsCount: formatResult.recommendations?.length || 0
    });

    let content = `# LLM Memory Test Results\n\n`;

    // Add memory retention visualization - scores from oldest to newest
    content += `## Memory Retention Visualization\n\n`;
    content += `\`\`\`\n`; // Start code block for the visualization

    // Sort results by factId to ensure they're in order from oldest to newest
    const sortedResults = [...evaluationResults].sort((a, b) => {
      const idA = parseInt(a.factId || a.id, 10);
      const idB = parseInt(b.factId || b.id, 10);
      return idA - idB; // Sort from oldest (lowest ID) to newest (highest ID)
    });

    // Create a visual bar for each fact
    for (const result of sortedResults) {
      const percentage = (result.score / 10) * 100;
      const formattedPercentage = this.formatPercentage(percentage);
      const complexityIndicator = result.complexity === 'complex' ? 'c' : 's';
      const bar = this.createPercentageBar(percentage);

      content += `${formattedPercentage} ${complexityIndicator} ${bar}\n`;
    }

    content += `\`\`\`\n`; // End code block

    content += `## Test Configuration\n\n`;
    content += `- **Session ID**: ${sessionId}\n`;
    content += `- **Memory Type**: ${memoryType}\n`;
    content += `- **Test Scenario**: ${testFactsFile || 'casual'}\n`;
    content += `- **Facts Tested**: ${testFactsCount}\n`;
    content += `- **Messages Between Facts**: ${messagesBetweenFacts}\n`;

    // Add model information
    content += `\n## Models Used\n\n`;
    content += `- **User Model**: ${userModel}\n`;
    content += `- **Assistant Model**: ${assistantModel}\n`;
    content += `- **Evaluator Model**: ${evaluatorModel}\n`;
    content += `- **Summary Model**: ${summaryModel}\n`;
    content += `- **Knowledge Extraction Model**: ${knowledgeExtractionModel}\n`;

    // Add scores for overall, simple, and complex facts
    content += `\n## Test Results\n\n`;
    if (
      typeof overallScore === 'object' &&
      overallScore.overall !== undefined
    ) {
      content += `- **Overall Score**: ${overallScore.overall.toFixed(2)}%\n`;
      content += `- **Simple Facts Score**: ${overallScore.simple.toFixed(2)}%\n`;
      content += `- **Complex Facts Score**: ${overallScore.complex.toFixed(2)}%\n`;
    } else {
      content += `- **Overall Score**: ${overallScore.toFixed(2)}%\n`;
    }

    // Add comprehensive performance metrics if available
    if (performanceMetrics) {
      content += `\n## Performance Metrics\n\n`;
      
      // Session-level performance
      content += `### Test Execution Performance\n\n`;
      content += `- **Total Duration**: ${(performanceMetrics.session.totalDuration / 1000).toFixed(2)}s\n`;
      content += `- **Operations Completed**: ${performanceMetrics.operations.completed}/${performanceMetrics.operations.total}\n`;
      content += `- **Operation Success Rate**: ${performanceMetrics.operations.successRate}%\n`;
      content += `- **Average Operation Duration**: ${Math.round(performanceMetrics.operations.averageDuration)}ms\n`;
      
      if (performanceMetrics.operations.longestOperation) {
        content += `- **Longest Operation**: ${performanceMetrics.operations.longestOperation.name} (${Math.round(performanceMetrics.operations.longestOperation.duration)}ms)\n`;
      }
      if (performanceMetrics.operations.shortestOperation) {
        content += `- **Shortest Operation**: ${performanceMetrics.operations.shortestOperation.name} (${Math.round(performanceMetrics.operations.shortestOperation.duration)}ms)\n`;
      }

      // API performance metrics
      if (apiStats) {
        content += `\n### API Performance\n\n`;
        content += `- **Total API Calls**: ${apiStats.overall.totalCalls}\n`;
        content += `- **API Success Rate**: ${apiStats.overall.successRate}%\n`;
        content += `- **Total Tokens Used**: ${apiStats.overall.totalTokens.toLocaleString()}\n`;
        content += `- **Average Response Time**: ${apiStats.overall.averageResponseTime}ms\n`;
        content += `- **Total Response Time**: ${(apiStats.overall.totalResponseTime / 1000).toFixed(2)}s\n`;

        // Per-model API statistics
        if (Object.keys(apiStats.byModel).length > 0) {
          content += `\n#### Per-Model API Statistics\n\n`;
          for (const [modelName, stats] of Object.entries(apiStats.byModel)) {
            content += `**${modelName}**:\n`;
            content += `- Calls: ${stats.calls} (${stats.successRate}% success)\n`;
            content += `- Tokens: ${stats.totalTokens.toLocaleString()} (avg: ${stats.averageTokensPerCall})\n`;
            content += `- Response Time: ${stats.averageResponseTime}ms avg (${stats.minResponseTime}-${stats.maxResponseTime}ms range)\n\n`;
          }
        }
      }

      // Memory performance metrics
      if (memoryStats) {
        content += `### Memory Performance\n\n`;
        content += `- **Current Heap Usage**: ${memoryStats.current.heapUsed}MB\n`;
        content += `- **Peak Heap Usage**: ${memoryStats.peak.heapUsed}MB\n`;
        content += `- **Heap Utilization**: ${memoryStats.current.heapUtilization}%\n`;
        content += `- **External Memory**: ${memoryStats.current.external}MB\n`;
        content += `- **RSS Memory**: ${memoryStats.current.rss}MB\n`;
        
        if (memoryStats.gcEvents > 0) {
          content += `- **Garbage Collection Events**: ${memoryStats.gcEvents}\n`;
        }
      }

      // System performance metrics
      if (systemStats) {
        content += `\n### System Performance\n\n`;
        content += `- **System Memory Usage**: ${systemStats.memory.utilization}% (${systemStats.memory.used}MB/${systemStats.memory.total}MB)\n`;
        content += `- **CPU Cores**: ${systemStats.load.cores}\n`;
        content += `- **Load Average**: [${systemStats.load.current.map(l => l.toFixed(2)).join(', ')}]\n`;
        content += `- **Process Uptime**: ${systemStats.uptime.process}s\n`;
        content += `- **System Uptime**: ${systemStats.uptime.system}s\n`;
      }

      // Operation-specific performance
      if (operationStats && operationStats.byName.length > 0) {
        content += `\n### Operation Performance Breakdown\n\n`;
        for (const opStats of operationStats.byName) {
          content += `**${opStats.name}**:\n`;
          content += `- Count: ${opStats.count} (${opStats.successRate}% success)\n`;
          content += `- Duration: ${opStats.averageDuration}ms avg (${opStats.minDuration}-${opStats.maxDuration}ms range)\n`;
          content += `- Total Time: ${Math.round(opStats.totalDuration)}ms\n\n`;
        }
      }
    }

    content += `\n## Evaluation Results\n\n`;

    // Group results by complexity if available
    if (evaluationResults.length > 0 && evaluationResults[0].complexity) {
      const simpleResults = evaluationResults.filter(
        result => result.complexity === 'simple'
      );
      const complexResults = evaluationResults.filter(
        result => result.complexity === 'complex'
      );

      // Add simple facts results
      content += `### Simple Facts\n\n`;
      for (const result of simpleResults) {
        content += `#### Fact ${result.factId || result.id}: "${result.fact}"\n\n`;
        content += `- **Question**: ${result.question}\n`;
        content += `- **Expected Answer**: ${result.expectedAnswer || result.answer}\n`;
        content += `- **Actual Response**: ${result.actualResponse || result.response}\n`;
        content += `- **Score**: ${result.score}/10\n`;

        // Add memory context if available
        if (result.memoryContext) {
          content += `\n- **Memory Context**:\n\n\`\`\`\n${result.memoryContext}\n\`\`\`\n`;
        }

        content += `\n`;
      }

      // Add complex facts results
      content += `### Complex Facts\n\n`;
      for (const result of complexResults) {
        content += `#### Fact ${result.factId || result.id}: "${result.fact}"\n\n`;
        content += `- **Question**: ${result.question}\n`;
        content += `- **Expected Answer**: ${result.expectedAnswer || result.answer}\n`;
        content += `- **Actual Response**: ${result.actualResponse || result.response}\n`;
        content += `- **Score**: ${result.score}/10\n`;

        // Add memory context if available
        if (result.memoryContext) {
          content += `\n- **Memory Context**:\n\n\`\`\`\n${result.memoryContext}\n\`\`\`\n`;
        }

        content += `\n`;
      }
    } else {
      // Original format if complexity is not available
      for (const result of evaluationResults) {
        content += `### Fact ${result.factId || result.id}: "${result.fact}"\n\n`;
        content += `- **Question**: ${result.question}\n`;
        content += `- **Expected Answer**: ${result.expectedAnswer || result.answer}\n`;
        content += `- **Actual Response**: ${result.actualResponse || result.response}\n`;
        content += `- **Score**: ${result.score}/10\n`;

        // Add memory context if available
        if (result.memoryContext) {
          content += `\n- **Memory Context**:\n\n\`\`\`\n${result.memoryContext}\n\`\`\`\n`;
        }

        content += `\n`;
      }
    }

    content += `## Conversation Log\n\n`;

    for (let i = 0; i < conversationLog.length; i++) {
      content += `### cycle ${i + 1}\n\n`;

      const message = conversationLog[i];
      content += `**${message.role}**: ${message.content}\n\n`;

      // Add memory context after each message if available
      if (memoryContexts && memoryContexts[i]) {
        content += `> **Memory Context at this point**:\n> \`\`\`\n> ${memoryContexts[i]}\n> \`\`\`\n\n`;
      }
    }

    await fs.writeFile(filePath, content, 'utf8');
  }
}

module.exports = Evaluator;
