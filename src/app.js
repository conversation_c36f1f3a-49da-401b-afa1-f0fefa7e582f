#!/usr/bin/env node

/**
 * LLM Memory Test Application - Main Entry Point
 *
 * This is the main application entry point for the LLM Memory Test framework.
 * It orchestrates the complete testing workflow including configuration validation,
 * memory system initialization, conversation simulation, and evaluation.
 *
 * The application tests different LLM memory implementations by:
 * 1. Loading and validating configuration
 * 2. Creating memory instances using the factory pattern
 * 3. Simulating conversations between user and assistant LLMs
 * 4. Injecting facts at specified intervals during conversation
 * 5. Evaluating memory retention through targeted questions
 * 6. Calculating and reporting performance scores
 * 7. Saving detailed results for analysis
 *
 * Features:
 * - Multiple memory implementation support (simple, summary, knowledge extraction)
 * - Configurable test scenarios and fact datasets
 * - Comprehensive error handling and validation
 * - Detailed logging and performance monitoring
 * - Batch testing across multiple scenarios
 * - Structured result output with visualization
 *
 * @fileoverview Main application entry point for LLM memory testing framework
 * <AUTHOR> Memory Test Team
 * @version 1.0.0
 * 
 * @example
 * // Run with default configuration
 * node src/app.js
 * 
 * @example
 * // Run with environment variables
 * MEMORY_TYPE=summary TEST_FACTS_COUNT=10 node src/app.js
 * 
 * @example
 * // Run all scenarios
 * TEST_FACTS_FILE=all node src/app.js
 */

require('dotenv').config();
const { v4: uuidv4 } = require('uuid');
const fs = require('fs');
const path = require('path');

// Import modules
const { ConfigManager } = require('./config');
const MemoryFactory = require('./memory/memoryFactory');
const ConversationSimulator = require('./utils/conversationSimulator');
const Evaluator = require('./utils/evaluator');
const PerformanceTracker = require('./utils/performanceTracker');
const {
  loadTestFacts,
  getAllTestFactScenarios
} = require('./utils/dataLoader');

// Initialize configuration
let config;
try {
  config = new ConfigManager();
  
  // Perform comprehensive configuration validation with feedback
  const validationResult = config.validateConfigurationWithFeedback();
  
  if (!validationResult.isValid) {
    console.error('Configuration validation failed:');
    validationResult.errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }
  
  // Display warnings if any
  if (validationResult.warnings.length > 0) {
    console.warn('\nConfiguration warnings:');
    validationResult.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
  
  // Display suggestions if any
  if (validationResult.suggestions.length > 0) {
    console.log('\nConfiguration suggestions:');
    validationResult.suggestions.forEach(suggestion => console.log(`  - ${suggestion}`));
  }
  
  console.log('Configuration loaded and validated successfully.');
  
} catch (error) {
  console.error('Configuration Error:', error.message);
  if (error.getFormattedMessage) {
    console.error('\nDetailed error:');
    console.error(error.getFormattedMessage());
  }
  process.exit(1);
}

/**
 * Execute a single memory test with comprehensive workflow orchestration
 * 
 * This function orchestrates the complete testing workflow for a single test scenario.
 * It handles memory initialization, conversation simulation, fact injection, evaluation,
 * scoring, and result persistence. The function provides detailed logging and error
 * handling throughout the process.
 * 
 * Workflow Steps:
 * 1. Generate unique session ID for tracking
 * 2. Load test facts from specified scenario
 * 3. Initialize memory system using factory pattern
 * 4. Create conversation simulator and evaluator instances
 * 5. Run conversation simulation with fact injection
 * 6. Evaluate memory retention through targeted questions
 * 7. Calculate performance scores (overall, simple, complex)
 * 8. Save detailed results to file (if configured)
 * 
 * @param {Object} appConfig - Complete test configuration object
 * @param {string} appConfig.memoryType - Memory implementation type ('simple', 'summary', 'summary_with_knowledge')
 * @param {string} appConfig.testFactsFile - Test scenario file identifier
 * @param {number} appConfig.testFactsCount - Number of facts to test
 * @param {number} appConfig.messagesBetweenFacts - Messages between fact injections
 * @param {boolean} appConfig.saveResults - Whether to save results to file
 * @param {string} appConfig.userModel - User model identifier
 * @param {string} appConfig.assistantModel - Assistant model identifier
 * @param {string} appConfig.evaluatorModel - Evaluator model identifier
 * @param {string} appConfig.summaryModel - Summary model identifier
 * @param {string} appConfig.knowledgeExtractionModel - Knowledge extraction model identifier
 * @param {string} [scenario] - Optional scenario name to override appConfig.testFactsFile
 * @returns {Promise<Object>} Test execution results
 * @returns {string} returns.sessionId - Unique session identifier
 * @returns {string} returns.testScenario - Scenario name that was executed
 * @returns {Object|number} returns.overallScore - Performance scores (object with overall/simple/complex or single number)
 * @returns {Array} returns.evaluationResults - Detailed evaluation results for each fact
 * @throws {Error} Throws if configuration is invalid
 * @throws {Error} Throws if test facts cannot be loaded
 * @throws {Error} Throws if memory initialization fails
 * @throws {Error} Throws if conversation simulation fails
 * @throws {Error} Throws if evaluation process fails
 * 
 * @example
 * const config = {
 *   memoryType: 'summary',
 *   testFactsFile: 'customer',
 *   testFactsCount: 5,
 *   messagesBetweenFacts: 3,
 *   saveResults: true,
 *   userModel: 'gpt-3.5-turbo',
 *   assistantModel: 'claude-3-sonnet',
 *   evaluatorModel: 'gpt-4'
 * };
 * const result = await runTest(config);
 * console.log(`Test completed with score: ${result.overallScore.overall}%`);
 * 
 * @example
 * // Override scenario
 * const result = await runTest(config, 'simple');
 * console.log(`Session: ${result.sessionId}, Scenario: ${result.testScenario}`);
 */
async function runTest(appConfig, scenario = null) {
  // Generate a unique session ID
  const sessionId = uuidv4();
  console.log(`Session ID: ${sessionId}`);

  // Initialize performance tracker for comprehensive monitoring
  const performanceTracker = new PerformanceTracker(sessionId, {
    enableSystemMonitoring: true,
    enableMemoryMonitoring: true,
    enableDetailedLogging: config.isVerboseLogging()
  });

  // Start overall test execution tracking
  const testExecutionId = performanceTracker.startOperation('test-execution', {
    type: 'test-workflow',
    component: 'main',
    metadata: {
      memoryType: appConfig.memoryType,
      testScenario: scenario || appConfig.testFactsFile,
      testFactsCount: appConfig.testFactsCount,
      messagesBetweenFacts: appConfig.messagesBetweenFacts
    }
  });

  // Override the test facts file if a scenario is provided
  const testScenario = scenario || appConfig.testFactsFile;
  console.log(`Memory type: ${appConfig.memoryType}`);
  console.log(`Test scenario: ${testScenario}`);
  console.log(
    `Testing ${appConfig.testFactsCount} facts with ${appConfig.messagesBetweenFacts} messages between facts`
  );

  // Load test facts with performance tracking
  console.log('Loading test facts...');
  const dataLoadingId = performanceTracker.startOperation('data-loading', {
    type: 'data-operation',
    component: 'dataLoader',
    metadata: { scenario: testScenario, count: appConfig.testFactsCount }
  });

  const testFacts = await loadTestFacts(appConfig.testFactsCount, testScenario, config);
  
  performanceTracker.endOperation(dataLoadingId, {
    success: true,
    metadata: { factsLoaded: testFacts.length }
  });

  // Initialize memory module with performance tracking
  console.log('Initializing memory system...');
  const memoryInitId = performanceTracker.startOperation('memory-initialization', {
    type: 'memory-operation',
    component: 'memoryFactory',
    metadata: { memoryType: appConfig.memoryType }
  });

  const memory = MemoryFactory.createMemory(appConfig.memoryType, sessionId, config);
  
  performanceTracker.endOperation(memoryInitId, {
    success: true,
    metadata: { memoryType: appConfig.memoryType }
  });

  // Initialize conversation simulator and evaluator
  const simulator = new ConversationSimulator(memory, sessionId, config);
  const evaluator = new Evaluator(sessionId, config);

  // Pass performance tracker to simulator and evaluator for integrated monitoring
  simulator.setPerformanceTracker(performanceTracker);
  evaluator.setPerformanceTracker(performanceTracker);

  // Run the conversation simulation with performance tracking
  console.log('Starting conversation simulation...');
  const conversationId = performanceTracker.startOperation('conversation-simulation', {
    type: 'conversation-operation',
    component: 'conversationSimulator',
    metadata: { 
      totalFacts: testFacts.length,
      messagesBetweenFacts: appConfig.messagesBetweenFacts
    }
  });

  const { conversationLog, usedFacts } = await simulator.runConversation(
    testFacts,
    appConfig.messagesBetweenFacts
  );

  performanceTracker.endOperation(conversationId, {
    success: true,
    metadata: { 
      messagesGenerated: conversationLog.length,
      factsUsed: usedFacts.length,
      factUtilization: ((usedFacts.length / testFacts.length) * 100).toFixed(1) + '%'
    }
  });

  // Evaluate memory retention with performance tracking
  console.log('Evaluating memory retention...');
  const evaluationId = performanceTracker.startOperation('memory-evaluation', {
    type: 'evaluation-operation',
    component: 'evaluator',
    metadata: { factsToEvaluate: usedFacts.length }
  });

  const evaluationResults = await evaluator.evaluateMemory(
    memory,
    usedFacts,
    conversationLog
  );

  performanceTracker.endOperation(evaluationId, {
    success: true,
    metadata: { 
      factsEvaluated: evaluationResults.length,
      averageScore: evaluationResults.reduce((sum, r) => sum + r.score, 0) / evaluationResults.length
    }
  });

  // Log the memory contexts for debugging
  if (config.isVerboseLogging()) {
    console.log('\nMemory contexts for each fact:');
    for (const result of evaluationResults) {
      console.log(`\nFact: "${result.fact}"`);
      console.log(`Question: ${result.question}`);
      console.log(
        `Memory Context:\n${result.memoryContext || 'No context provided'}`
      );
    }
  }

  // Calculate overall score
  const overallScore = evaluator.calculateOverallScore(evaluationResults);

  // End overall test execution tracking
  performanceTracker.endOperation(testExecutionId, {
    success: true,
    metadata: {
      overallScore: typeof overallScore === 'object' ? overallScore.overall : overallScore,
      simpleScore: typeof overallScore === 'object' ? overallScore.simple : null,
      complexScore: typeof overallScore === 'object' ? overallScore.complex : null,
      totalMessages: conversationLog.length,
      factsEvaluated: evaluationResults.length
    }
  });

  // Get comprehensive performance metrics
  const performanceMetrics = performanceTracker.getMetrics();
  const apiStats = performanceTracker.getApiStatistics();
  const memoryStats = performanceTracker.getMemoryStatistics();
  const systemStats = performanceTracker.getSystemStatistics();
  const operationStats = performanceTracker.getOperationStatistics();

  // Display performance summary
  console.log('\n=== Performance Summary ===');
  console.log(`Test Duration: ${(performanceMetrics.session.totalDuration / 1000).toFixed(2)}s`);
  console.log(`Total Operations: ${performanceMetrics.operations.total}`);
  console.log(`API Calls: ${performanceMetrics.api.totalCalls} (${performanceMetrics.api.successRate}% success)`);
  console.log(`Total Tokens: ${performanceMetrics.api.totalTokens.toLocaleString()}`);
  console.log(`Average Response Time: ${Math.round(performanceMetrics.api.averageResponseTime)}ms`);
  console.log(`Peak Memory Usage: ${memoryStats.peak.heapUsed}MB`);
  console.log(`System Memory Usage: ${systemStats.memory.utilization}%`);

  // Log scores based on the format of overallScore
  console.log('\n=== Test Results ===');
  if (typeof overallScore === 'object' && overallScore.overall !== undefined) {
    console.log(
      `Overall memory retention score: ${overallScore.overall.toFixed(2)}%`
    );
    console.log(`Simple facts score: ${overallScore.simple.toFixed(2)}%`);
    console.log(`Complex facts score: ${overallScore.complex.toFixed(2)}%`);
  } else {
    console.log(`Overall memory retention score: ${overallScore.toFixed(2)}%`);
  }

  // Save results if enabled
  if (config.shouldSaveResults()) {
    // Create test_results directory if it doesn't exist
    const testResultsDir = path.join(__dirname, '..', 'test_results');
    if (!fs.existsSync(testResultsDir)) {
      fs.mkdirSync(testResultsDir, { recursive: true });
    }

    let timestampSeconds = Math.floor(Date.now() / 1000);
    const resultsFilename = `results_${timestampSeconds}_${appConfig.memoryType}_${testScenario}_${sessionId}.md`;
    const resultsPath = path.join(testResultsDir, resultsFilename);

    await evaluator.saveResultsToFile(resultsPath, {
      sessionId,
      memoryType: appConfig.memoryType,
      testFactsFile: testScenario,
      testFactsCount: appConfig.testFactsCount,
      messagesBetweenFacts: appConfig.messagesBetweenFacts,
      // Model information
      userModel: appConfig.userModel,
      assistantModel: appConfig.assistantModel,
      evaluatorModel: appConfig.evaluatorModel,
      summaryModel: appConfig.summaryModel,
      knowledgeExtractionModel: appConfig.knowledgeExtractionModel,
      // Results
      overallScore,
      evaluationResults,
      conversationLog,
      memoryContexts: simulator.memoryContexts,
      // Performance metrics
      performanceMetrics,
      apiStats,
      memoryStats,
      systemStats,
      operationStats
    });

    console.log(`Results saved to test_results/${resultsFilename}`);
  }

  // Stop performance monitoring
  performanceTracker.stopSystemMonitoring();

  console.log(`Test for scenario ${testScenario} completed successfully!`);

  return {
    sessionId,
    testScenario,
    overallScore,
    evaluationResults,
    performanceMetrics,
    apiStats,
    memoryStats,
    systemStats,
    operationStats
  };
}

/**
 * Main application entry point and execution coordinator
 * 
 * This is the primary entry point for the LLM Memory Test Application. It handles
 * configuration loading, validation, and orchestrates either single scenario testing
 * or batch testing across all available scenarios. The function provides comprehensive
 * error handling and detailed progress reporting.
 * 
 * Execution Flow:
 * 1. Load and validate application configuration
 * 2. Create application configuration object from ConfigManager
 * 3. Determine execution mode (single scenario vs. all scenarios)
 * 4. Execute test(s) with appropriate error handling
 * 5. Display summary results and exit gracefully
 * 
 * The function supports two execution modes:
 * - Single scenario: Tests one specific memory scenario
 * - All scenarios: Sequentially tests all available scenarios and provides summary
 * 
 * @async
 * @function main
 * @returns {Promise<void>} Promise that resolves when application completes
 * @throws {Error} Throws if configuration loading fails
 * @throws {Error} Throws if scenario discovery fails
 * @throws {Error} Throws if any test execution fails
 * 
 * @example
 * // Called automatically when script is run
 * // node src/app.js
 * 
 * @example
 * // With environment configuration
 * // MEMORY_TYPE=summary TEST_FACTS_FILE=customer node src/app.js
 * 
 * @example
 * // Run all scenarios
 * // TEST_FACTS_FILE=all node src/app.js
 */
async function main() {
  console.log('Starting LLM Memory Test Application...');

  // Create configuration object from ConfigManager
  const appConfig = {
    memoryType: config.get('memory.type'),
    testFactsFile: config.get('test.factsFile'),
    testFactsCount: config.get('test.factsCount'),
    messagesBetweenFacts: config.get('test.messagesBetweenFacts'),
    saveResults: config.shouldSaveResults(),
    // Model information
    userModel: config.get('models.user.name'),
    assistantModel: config.get('models.assistant.name'),
    evaluatorModel: config.get('models.evaluator.name'),
    summaryModel: config.get('models.summary.name'),
    knowledgeExtractionModel: config.get('models.knowledgeExtraction.name')
  };

  // Check if we should run all scenarios
  if (appConfig.testFactsFile === 'all') {
    console.log('Running tests for all available scenarios sequentially');

    // Get all available scenarios
    const scenarios = await getAllTestFactScenarios();
    console.log(
      `Found ${scenarios.length} test scenarios: ${scenarios.join(', ')}`
    );

    // Run tests for each scenario
    const results = [];
    for (const scenario of scenarios) {
      console.log(`\n=== Starting test for scenario: ${scenario} ===\n`);
      const result = await runTest(appConfig, scenario);
      results.push(result);
      console.log(`\n=== Completed test for scenario: ${scenario} ===\n`);
    }

    // Log summary of all tests with performance comparison
    console.log('\n=== Summary of all tests ===');
    for (const result of results) {
      const score =
        typeof result.overallScore === 'object'
          ? result.overallScore.overall.toFixed(2)
          : result.overallScore.toFixed(2);
      console.log(`Scenario ${result.testScenario}: ${score}%`);
    }

    // Generate performance comparison report
    if (results.length > 1) {
      generatePerformanceComparison(results, appConfig.memoryType);
    }

    console.log('\nAll tests completed successfully!');
  } else {
    // Run a single test with the configured scenario
    await runTest(appConfig);
    console.log('Test completed successfully!');
  }

  process.exit(0);
}

/**
 * Generate performance comparison report for multiple test results
 * 
 * This function analyzes performance metrics across multiple test runs to provide
 * comparative insights and identify performance patterns. It's particularly useful
 * for comparing different memory implementations or test scenarios.
 * 
 * @param {Array<Object>} results - Array of test results with performance metrics
 * @param {string} memoryType - Memory type being tested for context
 */
function generatePerformanceComparison(results, memoryType) {
  console.log('\n=== Performance Comparison Report ===');
  console.log(`Memory Type: ${memoryType}`);
  console.log(`Scenarios Compared: ${results.length}`);

  // Calculate aggregate statistics
  const aggregateStats = {
    totalDuration: 0,
    totalApiCalls: 0,
    totalTokens: 0,
    totalOperations: 0,
    averageScore: 0,
    peakMemoryUsage: 0,
    scenarios: []
  };

  // Collect data from all results
  for (const result of results) {
    const scenario = {
      name: result.testScenario,
      score: typeof result.overallScore === 'object' ? result.overallScore.overall : result.overallScore,
      duration: result.performanceMetrics.session.totalDuration,
      apiCalls: result.performanceMetrics.api.totalCalls,
      tokens: result.performanceMetrics.api.totalTokens,
      operations: result.performanceMetrics.operations.total,
      peakMemory: result.memoryStats.peak.heapUsed,
      avgResponseTime: result.performanceMetrics.api.averageResponseTime
    };

    aggregateStats.scenarios.push(scenario);
    aggregateStats.totalDuration += scenario.duration;
    aggregateStats.totalApiCalls += scenario.apiCalls;
    aggregateStats.totalTokens += scenario.tokens;
    aggregateStats.totalOperations += scenario.operations;
    aggregateStats.averageScore += scenario.score;
    aggregateStats.peakMemoryUsage = Math.max(aggregateStats.peakMemoryUsage, scenario.peakMemory);
  }

  // Calculate averages
  aggregateStats.averageScore /= results.length;
  const avgDuration = aggregateStats.totalDuration / results.length;
  const avgApiCalls = aggregateStats.totalApiCalls / results.length;
  const avgTokens = aggregateStats.totalTokens / results.length;

  // Display aggregate statistics
  console.log('\n--- Aggregate Statistics ---');
  console.log(`Average Score: ${aggregateStats.averageScore.toFixed(2)}%`);
  console.log(`Average Duration: ${(avgDuration / 1000).toFixed(2)}s`);
  console.log(`Average API Calls: ${Math.round(avgApiCalls)}`);
  console.log(`Average Tokens: ${Math.round(avgTokens).toLocaleString()}`);
  console.log(`Peak Memory Usage: ${aggregateStats.peakMemoryUsage}MB`);

  // Find best and worst performing scenarios
  const sortedByScore = [...aggregateStats.scenarios].sort((a, b) => b.score - a.score);
  const sortedByDuration = [...aggregateStats.scenarios].sort((a, b) => a.duration - b.duration);
  const sortedByTokens = [...aggregateStats.scenarios].sort((a, b) => a.tokens - b.tokens);

  console.log('\n--- Performance Rankings ---');
  console.log(`Best Score: ${sortedByScore[0].name} (${sortedByScore[0].score.toFixed(2)}%)`);
  console.log(`Worst Score: ${sortedByScore[sortedByScore.length - 1].name} (${sortedByScore[sortedByScore.length - 1].score.toFixed(2)}%)`);
  console.log(`Fastest: ${sortedByDuration[0].name} (${(sortedByDuration[0].duration / 1000).toFixed(2)}s)`);
  console.log(`Slowest: ${sortedByDuration[sortedByDuration.length - 1].name} (${(sortedByDuration[sortedByDuration.length - 1].duration / 1000).toFixed(2)}s)`);
  console.log(`Most Efficient: ${sortedByTokens[0].name} (${sortedByTokens[0].tokens.toLocaleString()} tokens)`);
  console.log(`Least Efficient: ${sortedByTokens[sortedByTokens.length - 1].name} (${sortedByTokens[sortedByTokens.length - 1].tokens.toLocaleString()} tokens)`);

  // Performance efficiency analysis
  console.log('\n--- Efficiency Analysis ---');
  for (const scenario of aggregateStats.scenarios) {
    const scorePerSecond = scenario.score / (scenario.duration / 1000);
    const scorePerToken = scenario.tokens > 0 ? scenario.score / scenario.tokens * 1000 : 0; // Score per 1000 tokens
    const tokensPerSecond = scenario.tokens / (scenario.duration / 1000);

    console.log(`${scenario.name}:`);
    console.log(`  Score/Second: ${scorePerSecond.toFixed(2)}`);
    console.log(`  Score/1000 Tokens: ${scorePerToken.toFixed(2)}`);
    console.log(`  Tokens/Second: ${tokensPerSecond.toFixed(0)}`);
    console.log(`  Memory Efficiency: ${(scenario.score / scenario.peakMemory).toFixed(2)} score/MB`);
  }

  // Generate recommendations
  console.log('\n--- Performance Recommendations ---');
  
  const scoreVariance = calculateVariance(aggregateStats.scenarios.map(s => s.score));
  const durationVariance = calculateVariance(aggregateStats.scenarios.map(s => s.duration));
  
  if (scoreVariance < 25) { // Low variance in scores
    console.log('✓ Consistent performance across scenarios');
  } else {
    console.log('⚠ High performance variance - investigate scenario-specific optimizations');
  }

  if (durationVariance / (avgDuration * avgDuration) < 0.1) { // Low coefficient of variation
    console.log('✓ Consistent execution times across scenarios');
  } else {
    console.log('⚠ High execution time variance - some scenarios may need optimization');
  }

  const highTokenScenarios = aggregateStats.scenarios.filter(s => s.tokens > avgTokens * 1.5);
  if (highTokenScenarios.length > 0) {
    console.log(`⚠ High token usage in: ${highTokenScenarios.map(s => s.name).join(', ')}`);
  }

  const slowScenarios = aggregateStats.scenarios.filter(s => s.duration > avgDuration * 1.5);
  if (slowScenarios.length > 0) {
    console.log(`⚠ Slow execution in: ${slowScenarios.map(s => s.name).join(', ')}`);
  }

  console.log('\n=== End Performance Comparison ===');
}

/**
 * Calculate variance for an array of numbers
 * @param {Array<number>} values - Array of numeric values
 * @returns {number} Variance
 */
function calculateVariance(values) {
  if (values.length === 0) return 0;
  
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
  const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
  return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
}

// Run the application
main().catch(error => {
  console.error('Error running the application:', error);
  process.exit(1);
});
