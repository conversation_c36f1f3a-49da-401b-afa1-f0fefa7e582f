/**
 * Simple Memory Implementation
 *
 * A straightforward memory implementation that maintains the most recent N messages
 * in an in-memory array. This implementation provides fast access to recent conversation
 * history with automatic message rotation when the context window is exceeded.
 *
 * Features:
 * - Configurable context window size
 * - Automatic message rotation (FIFO)
 * - Comprehensive error handling and validation
 * - Performance monitoring and logging
 * - Thread-safe operations
 *
 * @example
 * // Basic usage
 * const memory = new SimpleMemory('session-123');
 * await memory.addMessage({ role: 'user', content: 'Hello' });
 * const context = await memory.getMemoryContext();
 *
 * @example
 * // With custom configuration
 * const config = new ConfigManager();
 * const memory = new SimpleMemory('session-456', config);
 * await memory.addMessage({
 *   role: 'assistant',
 *   content: 'Hi there!',
 *   timestamp: new Date().toISOString()
 * });
 */

const MemoryInterface = require('./memoryInterface');
const { ConfigManager } = require('../config');
const { MemoryError, ValidationError, ErrorFactory } = require('../utils/errors');
const { defaultLogger } = require('../utils/logger');

class SimpleMemory extends MemoryInterface {
  /**
   * Creates a new SimpleMemory instance
   * 
   * @param {string} sessionId - Unique session identifier for tracking conversations
   * @param {ConfigManager} [configManager] - Optional ConfigManager instance for configuration
   * @throws {ValidationError} If sessionId is invalid
   * @throws {ConfigurationError} If configuration is invalid
   * 
   * @example
   * const memory = new SimpleMemory('user-session-123');
   * 
   * @example
   * const config = new ConfigManager();
   * const memory = new SimpleMemory('session-456', config);
   */
  constructor(sessionId, configManager = null) {
    try {
      super(sessionId);
      
      // Initialize configuration
      this.config = configManager || new ConfigManager();
      
      // Create logger with session context
      this.logger = defaultLogger.child({ 
        component: 'SimpleMemory', 
        sessionId: this.sessionId 
      });
      
      // Initialize message storage
      this.messages = [];
      this._messageCount = 0;
      this._lastAccessTime = Date.now();
      
      // Load and validate configuration
      this._loadConfiguration();
      
      // Initialize performance tracking
      this._performanceStats = {
        addMessageCount: 0,
        getContextCount: 0,
        totalAddTime: 0,
        totalGetTime: 0,
        averageAddTime: 0,
        averageGetTime: 0
      };
      
      this.logger.info('SimpleMemory initialized successfully', {
        contextWindow: this.contextWindow,
        maxMessageLength: this.maxMessageLength,
        enableStrictValidation: this.enableStrictValidation
      });
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'constructor',
        sessionId,
        memoryType: 'SimpleMemory'
      });
      
      if (defaultLogger) {
        defaultLogger.error('Failed to initialize SimpleMemory', {
          error: wrappedError.getFormattedMessage(),
          sessionId
        });
      }
      
      throw wrappedError;
    }
  }

  /**
   * Load and validate configuration settings
   * @private
   * @throws {ConfigurationError} If configuration is invalid
   */
  _loadConfiguration() {
    try {
      // Load context window configuration
      this.contextWindow = this.config.get('memory.contextWindow');
      if (!Number.isInteger(this.contextWindow) || this.contextWindow < 1) {
        throw new ValidationError('Context window must be a positive integer', {
          field: 'memory.contextWindow',
          value: this.contextWindow
        });
      }
      
      // Load maximum message length (optional)
      this.maxMessageLength = this.config.get('memory.maxMessageLength', 10000);
      if (!Number.isInteger(this.maxMessageLength) || this.maxMessageLength < 1) {
        throw new ValidationError('Maximum message length must be a positive integer', {
          field: 'memory.maxMessageLength',
          value: this.maxMessageLength
        });
      }
      
      // Load validation settings
      this.enableStrictValidation = this.config.get('memory.enableStrictValidation', true);
      this.enablePerformanceLogging = this.config.get('memory.enablePerformanceLogging', true);
      
    } catch (error) {
      throw ErrorFactory.createError(error, {
        operation: 'loadConfiguration',
        memoryType: 'SimpleMemory'
      });
    }
  }

  /**
   * Add a message to memory with comprehensive validation and error handling
   * 
   * @param {Object} message - Message object to store
   * @param {string} message.role - Message role ('user', 'assistant', 'system')
   * @param {string} message.content - Message content/text
   * @param {string} [message.timestamp] - Optional timestamp (ISO string)
   * @param {Object} [message.metadata] - Optional additional metadata
   * @returns {Promise<void>} Promise that resolves when message is stored
   * @throws {ValidationError} If message format is invalid
   * @throws {MemoryError} If memory operation fails
   * 
   * @example
   * await memory.addMessage({
   *   role: 'user',
   *   content: 'What is the weather like?',
   *   timestamp: new Date().toISOString()
   * });
   * 
   * @example
   * await memory.addMessage({
   *   role: 'assistant',
   *   content: 'The weather is sunny today.',
   *   metadata: { confidence: 0.95, source: 'weather_api' }
   * });
   */
  async addMessage(message) {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('addMessage') : null;
    
    try {
      // Validate message input
      this._validateMessage(message);
      
      // Create a sanitized copy of the message
      const sanitizedMessage = this._sanitizeMessage(message);
      
      // Add message to storage
      this.messages.push(sanitizedMessage);
      this._messageCount++;
      this._lastAccessTime = Date.now();
      
      // Maintain context window size
      const removedMessages = this._maintainContextWindow();
      
      // Update performance statistics
      if (this.enablePerformanceLogging) {
        this._performanceStats.addMessageCount++;
        this._updatePerformanceStats('add');
      }
      
      this.logger.debug('Message added successfully', {
        messageRole: sanitizedMessage.role,
        messageLength: sanitizedMessage.content.length,
        totalMessages: this.messages.length,
        removedCount: removedMessages
      });
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'addMessage',
        memoryType: 'SimpleMemory',
        sessionId: this.sessionId,
        messageRole: message?.role,
        messageLength: message?.content?.length
      });
      
      this.logger.error('Failed to add message', {
        error: wrappedError.getFormattedMessage(),
        messagePreview: message?.content?.substring(0, 100)
      });
      
      throw wrappedError;
    } finally {
      if (timerId && this.enablePerformanceLogging) {
        this.logger.endTimer(timerId, 'addMessage');
      }
    }
  }

  /**
   * Get formatted memory context for the current conversation
   * 
   * @param {Object} [currentMessage] - Optional current message for context-aware formatting
   * @param {string} [currentMessage.role] - Current message role
   * @param {string} [currentMessage.content] - Current message content
   * @param {Object} [options] - Optional formatting options
   * @param {number} [options.maxLength] - Maximum context length in characters
   * @param {boolean} [options.includeMetadata] - Whether to include message metadata
   * @param {string} [options.format] - Output format ('text' or 'json')
   * @returns {Promise<string>} Promise that resolves to formatted memory context
   * @throws {MemoryError} If memory retrieval fails
   * 
   * @example
   * const context = await memory.getMemoryContext();
   * console.log(context); // "user: Hello\nassistant: Hi there!"
   * 
   * @example
   * const context = await memory.getMemoryContext(null, { 
   *   maxLength: 500, 
   *   includeMetadata: true 
   * });
   */
  async getMemoryContext(currentMessage, options = {}) {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('getMemoryContext') : null;
    
    try {
      // Update access time
      this._lastAccessTime = Date.now();
      
      // Validate options
      const validatedOptions = this._validateContextOptions(options);
      
      // Check if we have any messages
      if (this.messages.length === 0) {
        this.logger.debug('No messages in memory, returning empty context');
        return '';
      }
      
      // Format messages based on options
      let context = this._formatMessages(this.messages, validatedOptions);
      
      // Apply length limit if specified
      if (validatedOptions.maxLength && context.length > validatedOptions.maxLength) {
        context = this._truncateContext(context, validatedOptions.maxLength);
        this.logger.debug('Context truncated due to length limit', {
          originalLength: context.length,
          maxLength: validatedOptions.maxLength
        });
      }
      
      // Update performance statistics
      if (this.enablePerformanceLogging) {
        this._performanceStats.getContextCount++;
        this._updatePerformanceStats('get');
      }
      
      this.logger.debug('Memory context retrieved successfully', {
        messageCount: this.messages.length,
        contextLength: context.length,
        format: validatedOptions.format
      });
      
      return context;
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'getMemoryContext',
        memoryType: 'SimpleMemory',
        sessionId: this.sessionId,
        messageCount: this.messages.length
      });
      
      this.logger.error('Failed to get memory context', {
        error: wrappedError.getFormattedMessage()
      });
      
      throw wrappedError;
    } finally {
      if (timerId && this.enablePerformanceLogging) {
        this.logger.endTimer(timerId, 'getMemoryContext');
      }
    }
  }

  /**
   * Clear all stored memory for the current session
   * 
   * @returns {Promise<void>} Promise that resolves when memory is cleared
   * @throws {MemoryError} If memory clearing fails
   * 
   * @example
   * await memory.clearMemory();
   * console.log('Memory cleared for session:', memory.sessionId);
   */
  async clearMemory() {
    const timerId = this.enablePerformanceLogging ? 
      this.logger.startTimer('clearMemory') : null;
    
    try {
      const messageCount = this.messages.length;
      
      // Clear message storage
      this.messages = [];
      this._messageCount = 0;
      this._lastAccessTime = Date.now();
      
      // Reset performance statistics
      this._performanceStats = {
        addMessageCount: 0,
        getContextCount: 0,
        totalAddTime: 0,
        totalGetTime: 0,
        averageAddTime: 0,
        averageGetTime: 0
      };
      
      this.logger.info('Memory cleared successfully', {
        clearedMessageCount: messageCount
      });
      
    } catch (error) {
      const wrappedError = ErrorFactory.createError(error, {
        operation: 'clearMemory',
        memoryType: 'SimpleMemory',
        sessionId: this.sessionId
      });
      
      this.logger.error('Failed to clear memory', {
        error: wrappedError.getFormattedMessage()
      });
      
      throw wrappedError;
    } finally {
      if (timerId && this.enablePerformanceLogging) {
        this.logger.endTimer(timerId, 'clearMemory');
      }
    }
  }

  /**
   * Get enhanced memory statistics and metadata
   * 
   * @returns {Promise<Object>} Promise that resolves to comprehensive memory statistics
   * 
   * @example
   * const stats = await memory.getStats();
   * console.log('Memory performance:', stats.performance);
   */
  async getStats() {
    return {
      sessionId: this.sessionId,
      messageCount: this.messages.length,
      type: this.constructor.name,
      configuration: {
        contextWindow: this.contextWindow,
        maxMessageLength: this.maxMessageLength,
        enableStrictValidation: this.enableStrictValidation,
        enablePerformanceLogging: this.enablePerformanceLogging
      },
      performance: { ...this._performanceStats },
      lastAccessTime: new Date(this._lastAccessTime).toISOString(),
      memoryUsage: {
        estimatedSizeBytes: this._estimateMemoryUsage(),
        messagesInMemory: this.messages.length
      }
    };
  }

  // Private helper methods

  /**
   * Validate message format and content
   * @private
   * @param {Object} message - Message to validate
   * @throws {ValidationError} If message is invalid
   */
  _validateMessage(message) {
    if (!message || typeof message !== 'object') {
      throw new ValidationError('Message must be a non-null object', {
        field: 'message',
        value: typeof message
      });
    }

    // Validate required fields
    if (!message.role || typeof message.role !== 'string') {
      throw new ValidationError('Message role must be a non-empty string', {
        field: 'message.role',
        value: message.role
      });
    }

    if (!message.content || typeof message.content !== 'string') {
      throw new ValidationError('Message content must be a non-empty string', {
        field: 'message.content',
        value: typeof message.content
      });
    }

    // Validate role values (check sanitized version)
    const validRoles = ['user', 'assistant', 'system'];
    const sanitizedRole = message.role.trim().toLowerCase();
    if (this.enableStrictValidation && !validRoles.includes(sanitizedRole)) {
      throw new ValidationError('Message role must be one of: user, assistant, system', {
        field: 'message.role',
        value: message.role,
        validValues: validRoles
      });
    }

    // Validate content length
    if (message.content.length > this.maxMessageLength) {
      throw new ValidationError('Message content exceeds maximum length', {
        field: 'message.content',
        value: message.content.length,
        maxLength: this.maxMessageLength
      });
    }

    // Validate timestamp if provided
    if (message.timestamp && typeof message.timestamp === 'string') {
      const timestamp = new Date(message.timestamp);
      if (isNaN(timestamp.getTime())) {
        throw new ValidationError('Message timestamp must be a valid ISO string', {
          field: 'message.timestamp',
          value: message.timestamp
        });
      }
    }
  }

  /**
   * Create a sanitized copy of the message
   * @private
   * @param {Object} message - Original message
   * @returns {Object} Sanitized message
   */
  _sanitizeMessage(message) {
    const sanitized = {
      role: message.role.trim().toLowerCase(),
      content: message.content.trim(),
      timestamp: message.timestamp || new Date().toISOString()
    };

    // Include metadata if present
    if (message.metadata && typeof message.metadata === 'object') {
      sanitized.metadata = { ...message.metadata };
    }

    return sanitized;
  }

  /**
   * Maintain context window size by removing oldest messages
   * @private
   * @returns {number} Number of messages removed
   */
  _maintainContextWindow() {
    let removedCount = 0;
    
    while (this.messages.length > this.contextWindow) {
      this.messages.shift(); // Remove oldest message
      removedCount++;
    }

    if (removedCount > 0) {
      this.logger.debug('Removed old messages to maintain context window', {
        removedCount,
        remainingMessages: this.messages.length,
        contextWindow: this.contextWindow
      });
    }

    return removedCount;
  }

  /**
   * Validate context retrieval options
   * @private
   * @param {Object} options - Options to validate
   * @returns {Object} Validated options with defaults
   */
  _validateContextOptions(options) {
    const validated = {
      maxLength: null,
      includeMetadata: false,
      format: 'text',
      ...options
    };

    if (validated.maxLength !== null) {
      if (!Number.isInteger(validated.maxLength) || validated.maxLength < 1) {
        throw new ValidationError('maxLength must be a positive integer', {
          field: 'options.maxLength',
          value: validated.maxLength
        });
      }
    }

    if (typeof validated.includeMetadata !== 'boolean') {
      throw new ValidationError('includeMetadata must be a boolean', {
        field: 'options.includeMetadata',
        value: validated.includeMetadata
      });
    }

    if (!['text', 'json'].includes(validated.format)) {
      throw new ValidationError('format must be either "text" or "json"', {
        field: 'options.format',
        value: validated.format,
        validValues: ['text', 'json']
      });
    }

    return validated;
  }

  /**
   * Format messages according to specified options
   * @private
   * @param {Array} messages - Messages to format
   * @param {Object} options - Formatting options
   * @returns {string} Formatted messages
   */
  _formatMessages(messages, options) {
    if (options.format === 'json') {
      return JSON.stringify(messages, null, 2);
    }

    // Text format
    return messages.map(msg => {
      let formatted = `${msg.role}: ${msg.content}`;
      
      if (options.includeMetadata && msg.metadata) {
        formatted += ` [${JSON.stringify(msg.metadata)}]`;
      }
      
      return formatted;
    }).join('\n');
  }

  /**
   * Truncate context to fit within length limit
   * @private
   * @param {string} context - Context to truncate
   * @param {number} maxLength - Maximum allowed length
   * @returns {string} Truncated context
   */
  _truncateContext(context, maxLength) {
    if (context.length <= maxLength) {
      return context;
    }

    const truncationMarker = '\n... [truncated]';
    const availableLength = maxLength - truncationMarker.length;
    
    if (availableLength <= 0) {
      return truncationMarker.substring(1); // Remove leading newline
    }

    return context.substring(0, availableLength) + truncationMarker;
  }

  /**
   * Update performance statistics
   * @private
   * @param {string} operation - Operation type ('add' or 'get')
   */
  _updatePerformanceStats(operation) {
    const now = Date.now();
    
    if (operation === 'add') {
      const timeDiff = now - this._lastAddTime || 0;
      this._performanceStats.totalAddTime += timeDiff;
      this._performanceStats.averageAddTime = 
        this._performanceStats.totalAddTime / this._performanceStats.addMessageCount;
      this._lastAddTime = now;
    } else if (operation === 'get') {
      const timeDiff = now - this._lastGetTime || 0;
      this._performanceStats.totalGetTime += timeDiff;
      this._performanceStats.averageGetTime = 
        this._performanceStats.totalGetTime / this._performanceStats.getContextCount;
      this._lastGetTime = now;
    }
  }

  /**
   * Estimate memory usage in bytes
   * @private
   * @returns {number} Estimated memory usage in bytes
   */
  _estimateMemoryUsage() {
    let totalSize = 0;
    
    for (const message of this.messages) {
      // Rough estimation: each character is ~2 bytes in UTF-16
      totalSize += (message.content.length + message.role.length) * 2;
      
      // Add overhead for object structure and metadata
      totalSize += 100; // Base object overhead
      
      if (message.metadata) {
        totalSize += JSON.stringify(message.metadata).length * 2;
      }
    }
    
    return totalSize;
  }
}

module.exports = SimpleMemory;
