/**
 * Memory Factory - Factory Pattern for Memory Implementation Creation
 *
 * This factory class provides a centralized way to create different memory
 * implementations for the LLM Memory Test Application. It supports multiple
 * memory strategies and handles configuration management automatically.
 *
 * @class MemoryFactory
 * @static
 * 
 * @example
 * // Create a simple memory instance
 * const memory = MemoryFactory.createMemory('simple', 'session-123');
 * 
 * @example
 * // Create with custom configuration
 * const config = new ConfigManager();
 * const memory = MemoryFactory.createMemory('summary', 'session-456', config);
 * 
 * @example
 * // Get list of supported memory types
 * const types = MemoryFactory.getSupportedTypes();
 * console.log('Available memory types:', types);
 */

const SimpleMemory = require('./simpleMemory');
const InMemorySummaryMemory = require('./inMemorySummaryMemory');
const { ConfigManager } = require('../config');

class MemoryFactory {
  /**
   * Supported memory types and their descriptions
   * @private
   * @static
   * @readonly
   */
  static MEMORY_TYPES = {
    simple: {
      name: 'Simple Memory',
      description: 'Basic memory that stores all messages in chronological order',
      class: 'SimpleMemory',
      features: ['Message storage', 'Context window management', 'Basic retrieval']
    },
    summary: {
      name: 'Summary Memory',
      description: 'Memory with automatic summarization when context window is exceeded',
      class: 'InMemorySummaryMemory',
      features: ['Message storage', 'Automatic summarization', 'Context compression']
    },
    summary_with_knowledge: {
      name: 'Summary Memory with Knowledge Extraction',
      description: 'Advanced memory with summarization and knowledge extraction capabilities',
      class: 'InMemorySummaryMemory',
      features: ['Message storage', 'Automatic summarization', 'Knowledge extraction', 'Fact tracking']
    }
  };

  /**
   * Create a memory instance based on the specified type
   * 
   * This method instantiates the appropriate memory implementation based on the
   * provided type string. It handles configuration management and provides
   * fallback behavior for unknown types.
   * 
   * @static
   * @param {string} type - Memory type identifier
   * @param {string} sessionId - Unique session identifier for the memory instance
   * @param {ConfigManager} [configManager] - Optional ConfigManager instance for custom configuration
   * @returns {MemoryInterface} Memory instance implementing the MemoryInterface
   * @throws {Error} Throws error if sessionId is invalid
   * @throws {ConfigurationError} Throws if configuration is invalid
   * 
   * @example
   * // Create simple memory
   * const simpleMemory = MemoryFactory.createMemory('simple', 'session-123');
   * await simpleMemory.addMessage({ role: 'user', content: 'Hello' });
   * 
   * @example
   * // Create summary memory with knowledge extraction
   * const advancedMemory = MemoryFactory.createMemory(
   *   'summary_with_knowledge', 
   *   'session-456'
   * );
   * 
   * @example
   * // Create with custom configuration
   * const config = new ConfigManager();
   * config.set('memory.contextWindow', 20);
   * const memory = MemoryFactory.createMemory('summary', 'session-789', config);
   */
  static createMemory(type, sessionId, configManager = null) {
    // Validate inputs
    if (!sessionId || typeof sessionId !== 'string') {
      throw new Error('sessionId must be a non-empty string');
    }

    if (!type || typeof type !== 'string') {
      throw new Error('type must be a non-empty string');
    }

    const config = configManager || new ConfigManager();
    console.log(`Creating ${type} memory for session ${sessionId}`);

    // Validate memory type
    if (!this.isValidMemoryType(type)) {
      console.warn(
        `Unknown memory type: ${type}, falling back to simple memory. ` +
        `Supported types: ${this.getSupportedTypes().join(', ')}`
      );
      type = 'simple';
    }

    try {
      switch (type) {
        case 'simple':
          return new SimpleMemory(sessionId, config);
          
        case 'summary':
          return new InMemorySummaryMemory(sessionId, false, config);
          
        case 'summary_with_knowledge':
          return new InMemorySummaryMemory(sessionId, true, config);
          
        default:
          // This should not happen due to validation above, but included for safety
          console.warn(`Fallback: Creating simple memory for unknown type: ${type}`);
          return new SimpleMemory(sessionId, config);
      }
    } catch (error) {
      console.error(`Failed to create ${type} memory:`, error.message);
      throw new Error(`Memory creation failed: ${error.message}`);
    }
  }

  /**
   * Get list of supported memory types
   * 
   * @static
   * @returns {string[]} Array of supported memory type identifiers
   * 
   * @example
   * const types = MemoryFactory.getSupportedTypes();
   * console.log('Supported types:', types);
   * // Output: ['simple', 'summary', 'summary_with_knowledge']
   */
  static getSupportedTypes() {
    return Object.keys(this.MEMORY_TYPES);
  }

  /**
   * Check if a memory type is supported
   * 
   * @static
   * @param {string} type - Memory type to validate
   * @returns {boolean} True if the memory type is supported
   * 
   * @example
   * const isValid = MemoryFactory.isValidMemoryType('simple');
   * console.log('Is valid:', isValid); // true
   * 
   * @example
   * const isValid = MemoryFactory.isValidMemoryType('unknown');
   * console.log('Is valid:', isValid); // false
   */
  static isValidMemoryType(type) {
    return !!(type && typeof type === 'string' && type in this.MEMORY_TYPES);
  }

  /**
   * Get detailed information about a memory type
   * 
   * @static
   * @param {string} type - Memory type identifier
   * @returns {Object|null} Memory type information or null if not found
   * @returns {string} returns.name - Human-readable name
   * @returns {string} returns.description - Detailed description
   * @returns {string} returns.class - Implementation class name
   * @returns {string[]} returns.features - List of features
   * 
   * @example
   * const info = MemoryFactory.getMemoryTypeInfo('summary');
   * console.log(info);
   * // {
   * //   name: 'Summary Memory',
   * //   description: 'Memory with automatic summarization...',
   * //   class: 'InMemorySummaryMemory',
   * //   features: ['Message storage', 'Automatic summarization', ...]
   * // }
   */
  static getMemoryTypeInfo(type) {
    return this.MEMORY_TYPES[type] || null;
  }

  /**
   * Get all memory type information
   * 
   * @static
   * @returns {Object} Complete memory type registry
   * 
   * @example
   * const allTypes = MemoryFactory.getAllMemoryTypes();
   * Object.keys(allTypes).forEach(type => {
   *   console.log(`${type}: ${allTypes[type].name}`);
   * });
   */
  static getAllMemoryTypes() {
    return { ...this.MEMORY_TYPES };
  }

  /**
   * Create memory instance with validation and error handling
   * 
   * This method provides additional validation and error handling compared
   * to the basic createMemory method. It's recommended for production use.
   * 
   * @static
   * @param {Object} options - Creation options
   * @param {string} options.type - Memory type identifier
   * @param {string} options.sessionId - Session identifier
   * @param {ConfigManager} [options.configManager] - Configuration manager
   * @param {boolean} [options.validateConfig=true] - Whether to validate configuration
   * @returns {MemoryInterface} Created memory instance
   * @throws {Error} Throws detailed error if creation fails
   * 
   * @example
   * const memory = MemoryFactory.createMemoryWithValidation({
   *   type: 'summary_with_knowledge',
   *   sessionId: 'session-123',
   *   validateConfig: true
   * });
   */
  static createMemoryWithValidation(options) {
    // Comprehensive validation - check options first before destructuring
    if (!options || typeof options !== 'object') {
      throw new Error('Options object is required');
    }

    const { type, sessionId, configManager = null, validateConfig = true } = options;

    if (!type) {
      throw new Error('Memory type is required');
    }

    if (!sessionId) {
      throw new Error('Session ID is required');
    }

    if (!this.isValidMemoryType(type)) {
      throw new Error(
        `Invalid memory type: ${type}. Supported types: ${this.getSupportedTypes().join(', ')}`
      );
    }

    // Validate configuration if requested
    if (validateConfig && configManager) {
      try {
        configManager.validateConfiguration();
      } catch (error) {
        throw new Error(`Configuration validation failed: ${error.message}`);
      }
    }

    return this.createMemory(type, sessionId, configManager);
  }

  /**
   * Get recommended memory type based on use case requirements
   * 
   * @static
   * @param {Object} requirements - Use case requirements
   * @param {boolean} [requirements.needsSummarization=false] - Requires summarization
   * @param {boolean} [requirements.needsKnowledgeExtraction=false] - Requires knowledge extraction
   * @param {boolean} [requirements.highVolume=false] - High message volume expected
   * @param {boolean} [requirements.simplicity=false] - Prefer simple implementation
   * @returns {string} Recommended memory type
   * 
   * @example
   * const recommended = MemoryFactory.getRecommendedType({
   *   needsSummarization: true,
   *   needsKnowledgeExtraction: true,
   *   highVolume: true
   * });
   * console.log('Recommended:', recommended); // 'summary_with_knowledge'
   * 
   * @example
   * const recommended = MemoryFactory.getRecommendedType({
   *   simplicity: true
   * });
   * console.log('Recommended:', recommended); // 'simple'
   */
  static getRecommendedType(requirements = {}) {
    const {
      needsSummarization = false,
      needsKnowledgeExtraction = false,
      highVolume = false,
      simplicity = false
    } = requirements;

    if (simplicity && !needsSummarization && !needsKnowledgeExtraction) {
      return 'simple';
    }

    if (needsKnowledgeExtraction) {
      return 'summary_with_knowledge';
    }

    if (needsSummarization || highVolume) {
      return 'summary';
    }

    return 'simple';
  }
}

module.exports = MemoryFactory;
