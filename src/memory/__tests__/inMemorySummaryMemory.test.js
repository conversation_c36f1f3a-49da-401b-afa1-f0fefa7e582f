/**
 * Comprehensive tests for InMemorySummaryMemory implementation
 * Tests cover summarization logic, knowledge extraction, and error handling
 */

// Mock all dependencies BEFORE importing the module
jest.mock('../../config', () => ({
  ConfigManager: jest.fn()
}));

// Create a proper mock for LLMModel that prevents real API calls
jest.mock('../../models/llmModel', () => {
  return jest.fn().mockImplementation((modelName, config) => {
    return {
      generateResponse: jest.fn().mockResolvedValue('Mock LLM response'),
      modelName,
      config
    };
  });
});

// Import the module AFTER setting up mocks
const InMemorySummaryMemory = require('../inMemorySummaryMemory');

jest.mock('../../utils/logger', () => ({
  defaultLogger: {
    child: jest.fn(() => ({
      info: jest.fn(),
      debug: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      startTimer: jest.fn(() => 'timer-id'),
      endTimer: jest.fn()
    })),
    info: jest.fn(),
    error: jest.fn()
  }
}));

jest.mock('../../utils/errors', () => ({
  ValidationError: class ValidationError extends Error {
    constructor(message, context) {
      super(message);
      this.name = 'ValidationError';
      this.context = context;
    }
  },
  MemoryError: class MemoryError extends Error {
    constructor(message, context) {
      super(message);
      this.name = 'MemoryError';
      this.context = context;
    }
  },
  APIError: class APIError extends Error {
    constructor(message, context) {
      super(message);
      this.name = 'APIError';
      this.context = context;
    }
  },
  ErrorFactory: {
    createError: jest.fn((error, context) => {
      const wrappedError = new Error(error.message);
      wrappedError.context = context;
      return wrappedError;
    })
  }
}));

jest.mock('../../utils/prompts', () => ({
  prompts: {
    SUMMARY_PROMPT: 'Summarize the conversation',
    SUMMARY_UPDATE_PROMPT: 'Update the summary with new information',
    KNOWLEDGE_EXTRACTION_PROMPT: 'Extract knowledge about the user',
    KNOWLEDGE_MERGE_PROMPT: 'Merge new knowledge with existing'
  }
}));

const { ConfigManager } = require('../../config');
const LLMModel = require('../../models/llmModel');
const { ValidationError, MemoryError, APIError, ErrorFactory } = require('../../utils/errors');

describe('InMemorySummaryMemory', () => {
  let mockConfig;
  let mockSummaryModel;
  let mockKnowledgeModel;
  let memory;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Reset the LLM model call counter
    LLMModel.callCount = 0;

    // Setup mock config with actual default values
    mockConfig = {
      get: jest.fn((key, defaultValue) => {
        const config = {
          'memory.contextWindow': 10,
          'memory.summaryThreshold': 20,
          'memory.maxSummaryLength': 2000,
          'memory.maxKnowledgeLength': 1000,
          'memory.enableSummaryValidation': true,
          'memory.enableFallbackStrategies': true,
          'memory.maxRetryAttempts': 3,
          'memory.enablePerformanceLogging': true,
          'models.summary.name': 'openai/gpt-3.5-turbo',
          'models.knowledgeExtraction.name': 'openai/gpt-3.5-turbo'
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      })
    };

    // Setup mock LLM models with default responses
    mockSummaryModel = {
      generateResponse: jest.fn().mockResolvedValue('Generated summary')
    };
    mockKnowledgeModel = {
      generateResponse: jest.fn().mockResolvedValue('Extracted knowledge')
    };

    // Mock ConfigManager and LLMModel constructors
    ConfigManager.mockImplementation(() => mockConfig);
    LLMModel.mockImplementation((modelName, config) => {
      // Track constructor calls for testing
      // First call is summary model, second call is knowledge model
      LLMModel.callCount++;
      if (LLMModel.callCount === 1) {
        return mockSummaryModel;
      } else {
        return mockKnowledgeModel;
      }
    });
  });

  describe('Constructor', () => {
    test('should create InMemorySummaryMemory without knowledge extraction', () => {
      memory = new InMemorySummaryMemory('session-123');
      
      expect(memory.sessionId).toBe('session-123');
      expect(memory.enableKnowledgeExtraction).toBe(false);
      expect(memory.storage.messages).toEqual([]);
      expect(memory.storage.summary).toBe('');
      expect(memory.storage.knowledge).toBe('');
      expect(memory.recentMessagesCount).toBe(10);
      expect(memory.summaryThreshold).toBe(20);
    });

    test('should create InMemorySummaryMemory with knowledge extraction enabled', () => {
      memory = new InMemorySummaryMemory('session-123', true);
      
      expect(memory.enableKnowledgeExtraction).toBe(true);
      // Note: LLM models are created lazily, so constructor calls may not happen immediately
    });

    test('should create with custom configuration', () => {
      const customConfig = {
        get: jest.fn((key, defaultValue) => {
          const config = {
            'memory.contextWindow': 10,
            'memory.summaryThreshold': 5,
            'models.summary.name': 'gpt-4',
            'models.knowledgeExtraction.name': 'gpt-4'
          };
          return config[key] !== undefined ? config[key] : defaultValue;
        })
      };

      memory = new InMemorySummaryMemory('session-456', false, customConfig);
      
      expect(memory.recentMessagesCount).toBe(10);
      expect(memory.summaryThreshold).toBe(5);
    });

    test('should throw error with invalid configuration', () => {
      mockConfig.get.mockImplementation((key) => {
        if (key === 'memory.contextWindow') return 0; // Invalid
        if (key === 'memory.summaryThreshold') return 3;
        if (key === 'models.summary.name') return 'gpt-3.5-turbo';
        return undefined;
      });

      expect(() => {
        new InMemorySummaryMemory('session-123');
      }).toThrow('Context window must be a positive integer');
    });

    test('should throw error when summary model is missing', () => {
      // Clear and reset the mock
      mockConfig.get.mockClear();
      mockConfig.get.mockImplementation((key, defaultValue) => {
        const config = {
          'memory.contextWindow': 10,
          'memory.summaryThreshold': 20,
          'memory.maxSummaryLength': 2000,
          'memory.maxKnowledgeLength': 1000,
          'memory.enableSummaryValidation': true,
          'memory.enableFallbackStrategies': true,
          'memory.maxRetryAttempts': 3,
          'memory.enablePerformanceLogging': true
          // Note: models.summary.name is intentionally missing
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      });

      expect(() => {
        new InMemorySummaryMemory('session-123');
      }).toThrow('Failed to initialize LLM models');
    });

    test('should throw error when knowledge model is missing but knowledge extraction is enabled', () => {
      // Clear and reset the mock
      mockConfig.get.mockClear();
      mockConfig.get.mockImplementation((key, defaultValue) => {
        const config = {
          'memory.contextWindow': 10,
          'memory.summaryThreshold': 20,
          'memory.maxSummaryLength': 2000,
          'memory.maxKnowledgeLength': 1000,
          'memory.enableSummaryValidation': true,
          'memory.enableFallbackStrategies': true,
          'memory.maxRetryAttempts': 3,
          'memory.enablePerformanceLogging': true,
          'models.summary.name': 'openai/gpt-3.5-turbo'
          // Note: models.knowledgeExtraction.name is intentionally missing
        };
        return config[key] !== undefined ? config[key] : defaultValue;
      });

      expect(() => {
        new InMemorySummaryMemory('session-123', true); // Knowledge extraction enabled
      }).toThrow('Failed to initialize LLM models');
    });
  });

  describe('addMessage', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123');
      mockSummaryModel.generateResponse.mockResolvedValue('Generated summary');
    });

    test('should add message without triggering summarization', async () => {
      const message = {
        role: 'user',
        content: 'Hello world'
      };

      await memory.addMessage(message);

      expect(memory.storage.messages).toHaveLength(1);
      expect(memory.storage.messages[0]).toEqual(expect.objectContaining({
        role: 'user',
        content: 'Hello world'
      }));
      expect(mockSummaryModel.generateResponse).not.toHaveBeenCalled();
    });

    test('should trigger summarization when threshold is reached', async () => {
      // Add messages up to the trigger point (contextWindow + summaryThreshold = 10 + 20 = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Message ${i}`
        });
      }

      expect(mockSummaryModel.generateResponse).toHaveBeenCalled();
      expect(memory.storage.summary).toBe('Generated summary');
      expect(memory.storage.messages).toHaveLength(10); // Only recent messages kept
      expect(memory.storage.metadata.summarizationCount).toBe(1);
    });

    test('should handle summarization with existing summary', async () => {
      memory.storage.summary = 'Existing summary';
      
      // Add messages to trigger summarization (need 30 messages for default threshold)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      const callArgs = mockSummaryModel.generateResponse.mock.calls[0];
      const promptMessages = callArgs[1];
      expect(promptMessages[0].content).toContain('Previous summary:');
      expect(promptMessages[0].content).toContain('Existing summary');
    });

    test('should handle summarization errors with retry', async () => {
      // Clear the mock and set up the retry scenario
      mockSummaryModel.generateResponse.mockClear();
      mockSummaryModel.generateResponse
        .mockRejectedValueOnce(new Error('API Error'))
        .mockResolvedValueOnce('Generated summary after retry');

      // Add enough messages to trigger summarization (contextWindow + summaryThreshold = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      expect(mockSummaryModel.generateResponse).toHaveBeenCalledTimes(2);
      expect(memory.storage.summary).toBe('Generated summary after retry');
    });

    test('should throw error after max retry attempts', async () => {
      mockSummaryModel.generateResponse.mockRejectedValue(new Error('Persistent API Error'));

      // Add enough messages to trigger summarization (contextWindow + summaryThreshold = 30)
      for (let i = 0; i < 29; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      await expect(memory.addMessage({
        role: 'user',
        content: 'Final message'
      })).rejects.toThrow('Persistent API Error');
    });

    test('should validate message format', async () => {
      await expect(memory.addMessage(null)).rejects.toThrow('Message must be a non-null object');
      await expect(memory.addMessage({})).rejects.toThrow('Message role must be a non-empty string');
      await expect(memory.addMessage({ role: 'user' })).rejects.toThrow('Message content must be a non-empty string');
      await expect(memory.addMessage({ content: 'test' })).rejects.toThrow('Message role must be a non-empty string');
    });

    test('should update performance statistics', async () => {
      await memory.addMessage({ role: 'user', content: 'Test' });
      
      const stats = await memory.getStats();
      expect(stats.performance.addMessageCount).toBe(1);
      expect(stats.storage.metadata.totalMessagesProcessed).toBe(1);
    });
  });

  describe('getMemoryContext', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123', true); // Enable knowledge extraction
    });

    test('should return empty string for no content', async () => {
      const context = await memory.getMemoryContext();
      expect(context).toBe('');
    });

    test('should return only recent messages when no summary exists', async () => {
      await memory.addMessage({ role: 'user', content: 'Hello' });
      await memory.addMessage({ role: 'assistant', content: 'Hi there!' });

      const context = await memory.getMemoryContext();
      
      expect(context).toContain('Recent conversation:');
      expect(context).toContain('user: Hello');
      expect(context).toContain('assistant: Hi there!');
      expect(context).not.toContain('Previous conversation summary:');
    });

    test('should include summary when available', async () => {
      memory.storage.summary = 'This is a test summary';
      await memory.addMessage({ role: 'user', content: 'New message' });

      const context = await memory.getMemoryContext();
      
      expect(context).toContain('Previous conversation summary:');
      expect(context).toContain('This is a test summary');
      expect(context).toContain('Recent conversation:');
      expect(context).toContain('user: New message');
    });

    test('should include knowledge when enabled and available', async () => {
      memory = new InMemorySummaryMemory('session-123', true);
      memory.storage.knowledge = 'User likes coffee and works in tech';
      await memory.addMessage({ role: 'user', content: 'Hello' });

      const context = await memory.getMemoryContext();
      
      expect(context).toContain('Important information about the user:');
      expect(context).toContain('User likes coffee and works in tech');
    });

    test('should not include knowledge section when knowledge extraction is disabled', async () => {
      memory = new InMemorySummaryMemory('session-123', false);
      memory.storage.knowledge = 'This should not appear';
      await memory.addMessage({ role: 'user', content: 'Hello' });

      const context = await memory.getMemoryContext();
      
      expect(context).not.toContain('Important information about the user:');
      expect(context).not.toContain('This should not appear');
    });

    test('should return JSON format when requested', async () => {
      memory.storage.summary = 'Test summary';
      memory.storage.knowledge = 'Test knowledge';
      await memory.addMessage({ role: 'user', content: 'Test message' });

      const context = await memory.getMemoryContext(null, { format: 'json' });
      
      const parsed = JSON.parse(context);
      expect(parsed).toEqual(expect.objectContaining({
        summary: 'Test summary',
        knowledge: 'Test knowledge',
        recentMessages: expect.arrayContaining([
          expect.objectContaining({
            role: 'user',
            content: 'Test message'
          })
        ]),
        metadata: expect.any(Object)
      }));
    });

    test('should truncate context when maxLength specified', async () => {
      memory.storage.summary = 'A very long summary that should be truncated when the maximum length is specified for the context output';
      
      const context = await memory.getMemoryContext(null, { maxLength: 50 });
      
      expect(context.length).toBeLessThanOrEqual(50);
      expect(context).toContain('... [truncated]');
    });

    test('should include metadata when requested', async () => {
      await memory.addMessage({
        role: 'user',
        content: 'Test',
        metadata: { confidence: 0.9 }
      });

      const context = await memory.getMemoryContext(null, { includeMetadata: true });
      
      expect(context).toContain('{"confidence":0.9}');
    });

    test('should update performance statistics', async () => {
      await memory.getMemoryContext();
      await memory.getMemoryContext();

      const stats = await memory.getStats();
      expect(stats.performance.getContextCount).toBe(2);
    });
  });

  describe('clearMemory', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123', true);
    });

    test('should clear all memory components', async () => {
      // Add some content
      await memory.addMessage({ role: 'user', content: 'Test' });
      memory.storage.summary = 'Test summary';
      memory.storage.knowledge = 'Test knowledge';
      memory.storage.metadata.summarizationCount = 5;

      await memory.clearMemory();

      expect(memory.storage.messages).toHaveLength(0);
      expect(memory.storage.summary).toBe('');
      expect(memory.storage.knowledge).toBe('');
      expect(memory.storage.metadata.totalMessagesProcessed).toBe(0);
      expect(memory.storage.metadata.summarizationCount).toBe(0);
    });

    test('should reset performance statistics', async () => {
      await memory.addMessage({ role: 'user', content: 'Test' });
      await memory.getMemoryContext();

      await memory.clearMemory();

      const stats = await memory.getStats();
      expect(stats.performance.addMessageCount).toBe(0);
      expect(stats.performance.getContextCount).toBe(0);
      expect(stats.performance.summarizationCount).toBe(0);
      expect(stats.performance.knowledgeExtractionCount).toBe(0);
    });
  });

  describe('summarizeOldMessages', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123');
      mockSummaryModel.generateResponse.mockResolvedValue('Generated summary');
    });

    test('should not summarize when insufficient messages', async () => {
      await memory.addMessage({ role: 'user', content: 'Test' });

      await memory.summarizeOldMessages();

      expect(mockSummaryModel.generateResponse).not.toHaveBeenCalled();
      expect(memory.storage.summary).toBe('');
    });

    test('should summarize old messages and keep recent ones', async () => {
      // Add enough messages to trigger summarization (need contextWindow + summaryThreshold = 10 + 20 = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Message ${i}`
        });
      }

      await memory.summarizeOldMessages();

      expect(mockSummaryModel.generateResponse).toHaveBeenCalled();
      expect(memory.storage.summary).toBe('Generated summary');
      expect(memory.storage.messages).toHaveLength(10); // recentMessagesCount (contextWindow)
      expect(memory.storage.metadata.summarizationCount).toBeGreaterThan(0);
    });

    test('should update existing summary', async () => {
      memory.storage.summary = 'Existing summary';

      // Add enough messages (need contextWindow + summaryThreshold = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      await memory.summarizeOldMessages();

      const callArgs = mockSummaryModel.generateResponse.mock.calls[0];
      const promptMessages = callArgs[1];
      expect(promptMessages[0].content).toContain('Previous summary:');
      expect(promptMessages[0].content).toContain('Existing summary');
    });

    test('should handle API errors with retry', async () => {
      mockSummaryModel.generateResponse
        .mockRejectedValueOnce(new APIError('Rate limited'))
        .mockResolvedValueOnce('Summary after retry');

      // Add enough messages (need contextWindow + summaryThreshold = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      await memory.summarizeOldMessages();

      expect(mockSummaryModel.generateResponse).toHaveBeenCalledTimes(2);
      expect(memory.storage.summary).toBe('Summary after retry');
    });

    test('should update performance statistics', async () => {
      // Add enough messages (need contextWindow + summaryThreshold = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      await memory.summarizeOldMessages();

      const stats = await memory.getStats();
      expect(stats.performance.summarizationCount).toBeGreaterThan(0);
    });
  });

  describe('extractAndUpdateKnowledge', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123', true);
      mockKnowledgeModel.generateResponse.mockResolvedValue('Extracted knowledge');
    });

    test('should skip when knowledge extraction is disabled', async () => {
      memory = new InMemorySummaryMemory('session-123', false);
      
      await memory.extractAndUpdateKnowledge();

      expect(mockKnowledgeModel.generateResponse).not.toHaveBeenCalled();
    });

    test('should extract knowledge from provided messages', async () => {
      const messages = [
        { role: 'user', content: 'My name is John and I work as a developer' },
        { role: 'assistant', content: 'Nice to meet you, John!' }
      ];

      await memory.extractAndUpdateKnowledge(messages);

      expect(mockKnowledgeModel.generateResponse).toHaveBeenCalled();
      expect(memory.storage.knowledge).toBe('Extracted knowledge');
      expect(memory.storage.metadata.knowledgeExtractionCount).toBe(1);
    });

    test('should extract knowledge from stored messages when none provided', async () => {
      // Add messages beyond context window (need more than contextWindow = 10)
      for (let i = 0; i < 15; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i} with personal info`
        });
      }

      await memory.extractAndUpdateKnowledge();

      expect(mockKnowledgeModel.generateResponse).toHaveBeenCalled();
      expect(memory.storage.knowledge).toBe('Extracted knowledge');
    });

    test('should merge with existing knowledge', async () => {
      memory.storage.knowledge = 'Existing knowledge about user';
      
      const messages = [
        { role: 'user', content: 'I also like pizza' }
      ];

      await memory.extractAndUpdateKnowledge(messages);

      const callArgs = mockKnowledgeModel.generateResponse.mock.calls[0];
      // The second argument is the messages array
      const promptMessages = callArgs[1];
      expect(Array.isArray(promptMessages)).toBe(true);
      expect(promptMessages[0].content).toContain('Existing knowledge about the user:');
      expect(promptMessages[0].content).toContain('Existing knowledge about user');
    });

    test('should handle API errors with retry', async () => {
      // Clear the mock and set up the retry scenario
      mockKnowledgeModel.generateResponse.mockClear();
      mockKnowledgeModel.generateResponse
        .mockRejectedValueOnce(new APIError('Service unavailable'))
        .mockResolvedValueOnce('Knowledge after retry');

      const messages = [
        { role: 'user', content: 'Test message with info' }
      ];

      await memory.extractAndUpdateKnowledge(messages);

      expect(mockKnowledgeModel.generateResponse).toHaveBeenCalledTimes(2);
      expect(memory.storage.knowledge).toBe('Knowledge after retry');
    });

    test('should not extract when insufficient messages', async () => {
      await memory.addMessage({ role: 'user', content: 'Test' });

      await memory.extractAndUpdateKnowledge();

      expect(mockKnowledgeModel.generateResponse).not.toHaveBeenCalled();
    });

    test('should update performance statistics', async () => {
      const messages = [
        { role: 'user', content: 'Test message' }
      ];

      await memory.extractAndUpdateKnowledge(messages);

      const stats = await memory.getStats();
      expect(stats.performance.knowledgeExtractionCount).toBe(1);
    });
  });

  describe('getStats', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123', true);
    });

    test('should return comprehensive statistics', async () => {
      await memory.addMessage({ role: 'user', content: 'Test message' });

      const stats = await memory.getStats();

      expect(stats).toEqual(expect.objectContaining({
        sessionId: 'session-123',
        messageCount: 1,
        type: 'InMemorySummaryMemory',
        configuration: expect.objectContaining({
          enableKnowledgeExtraction: true,
          recentMessagesCount: 10,
          summaryThreshold: 20,
          maxSummaryLength: 2000,
          maxKnowledgeLength: 1000
        }),
        performance: expect.objectContaining({
          addMessageCount: expect.any(Number),
          getContextCount: expect.any(Number),
          summarizationCount: expect.any(Number),
          knowledgeExtractionCount: expect.any(Number)
        }),
        storage: expect.objectContaining({
          hasSummary: false,
          hasKnowledge: false,
          summaryLength: 0,
          knowledgeLength: 0,
          messageCount: 1,
          metadata: expect.any(Object)
        }),
        memoryUsage: expect.any(Object)
      }));
    });

    test('should track storage state correctly', async () => {
      memory.storage.summary = 'Test summary';
      memory.storage.knowledge = 'Test knowledge';

      const stats = await memory.getStats();

      expect(stats.storage.hasSummary).toBe(true);
      expect(stats.storage.hasKnowledge).toBe(true);
      expect(stats.storage.summaryLength).toBe('Test summary'.length);
      expect(stats.storage.knowledgeLength).toBe('Test knowledge'.length);
    });
  });

  describe('Error Handling', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123');
    });

    test('should wrap and rethrow errors with context', async () => {
      // Clear mocks before the test to ensure clean state
      ErrorFactory.createError.mockClear();

      // Mock validation to throw an error
      const originalValidate = memory._validateMessage;
      memory._validateMessage = jest.fn(() => {
        throw new Error('Validation failed');
      });

      let caughtError = null;
      try {
        await memory.addMessage({ role: 'user', content: 'test' });
        fail('Expected error to be thrown');
      } catch (error) {
        caughtError = error;
      }

      // Check that an error was caught
      expect(caughtError).not.toBeNull();

      // Check that the validation method was called
      expect(memory._validateMessage).toHaveBeenCalled();

      // The error should be wrapped, so check if it has context
      expect(caughtError.context).toBeDefined();
      expect(caughtError.context.operation).toBe('addMessage');
      expect(caughtError.context.memoryType).toBe('InMemorySummaryMemory');
      expect(caughtError.context.sessionId).toBe('session-123');

      // Restore original method
      memory._validateMessage = originalValidate;
    });

    test('should handle LLM model initialization errors', () => {
      LLMModel.mockImplementation(() => {
        throw new Error('Model initialization failed');
      });

      expect(() => {
        new InMemorySummaryMemory('session-123');
      }).toThrow('Failed to initialize LLM models');
    });

    test('should handle summarization validation errors', async () => {
      mockSummaryModel.generateResponse.mockResolvedValue(''); // Empty response

      // Add enough messages to trigger summarization and test error handling
      try {
        for (let i = 0; i < 30; i++) {
          await memory.addMessage({
            role: 'user',
            content: `Message ${i}`
          });
        }
        fail('Expected validation error to be thrown');
      } catch (error) {
        expect(error.message).toContain('Summary must be a non-empty string');
      }
    });
  });

  describe('Integration Scenarios', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123', true);
      mockSummaryModel.generateResponse.mockResolvedValue('Generated summary');
      mockKnowledgeModel.generateResponse.mockResolvedValue('Extracted knowledge');
    });

    test('should handle complete conversation flow with summarization and knowledge extraction', async () => {
      // Add enough messages to trigger summarization (contextWindow + summaryThreshold = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: i % 2 === 0 ? 'user' : 'assistant',
          content: `Message ${i} with some personal information`
        });
      }

      // Verify summarization occurred
      expect(memory.storage.summary).toBe('Generated summary');
      expect(memory.storage.messages).toHaveLength(10); // contextWindow

      // Get context and verify it includes all components
      const context = await memory.getMemoryContext();
      expect(context).toContain('Previous conversation summary:');
      expect(context).toContain('Recent conversation:');

      // Verify statistics
      const stats = await memory.getStats();
      expect(stats.performance.summarizationCount).toBeGreaterThan(0);
      expect(stats.storage.hasSummary).toBe(true);
    });

    test('should handle multiple summarization cycles', async () => {
      // First cycle - add enough messages to trigger first summarization (30 messages)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `First cycle message ${i}`
        });
      }

      expect(memory.storage.metadata.summarizationCount).toBe(1);
      expect(memory.storage.messages).toHaveLength(10); // contextWindow messages remain

      // Second cycle - add enough messages to trigger second summarization (20 more messages)
      for (let i = 0; i < 20; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Second cycle message ${i}`
        });
      }

      expect(memory.storage.metadata.summarizationCount).toBe(2);
      expect(memory.storage.messages).toHaveLength(10); // Still only recent messages (contextWindow)
    });

    test('should maintain performance under load', async () => {
      const startTime = Date.now();

      // Add many messages to trigger multiple summarizations
      for (let i = 0; i < 50; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Load test message ${i}`
        });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete in reasonable time
      expect(duration).toBeLessThan(5000); // 5 seconds
      expect(memory.storage.messages).toHaveLength(10); // Memory bounded to contextWindow
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    beforeEach(() => {
      memory = new InMemorySummaryMemory('session-123');
      mockSummaryModel.generateResponse.mockResolvedValue('Generated summary response');
    });

    test('should handle exactly threshold number of messages', async () => {
      // Add exactly contextWindow + summaryThreshold messages (10 + 20 = 30)
      for (let i = 0; i < 30; i++) {
        await memory.addMessage({
          role: 'user',
          content: `Message ${i}`
        });
      }

      expect(mockSummaryModel.generateResponse).toHaveBeenCalled();
      expect(memory.storage.messages).toHaveLength(10); // contextWindow
    });

    test('should handle empty summary response', async () => {
      mockSummaryModel.generateResponse.mockResolvedValue('');

      // Add enough messages to trigger summarization and test error handling
      try {
        for (let i = 0; i < 30; i++) {
          await memory.addMessage({
            role: 'user',
            content: `Message ${i}`
          });
        }
        fail('Expected validation error to be thrown');
      } catch (error) {
        expect(error.message).toContain('Summary must be a non-empty string');
      }
    });

    test('should handle very long messages', async () => {
      const longMessage = {
        role: 'user',
        content: 'a'.repeat(5000)
      };

      await memory.addMessage(longMessage);
      expect(memory.storage.messages[0].content).toBe('a'.repeat(5000));
    });

    test('should handle concurrent operations', async () => {
      const promises = [];
      
      // Add messages concurrently
      for (let i = 0; i < 5; i++) {
        promises.push(memory.addMessage({
          role: 'user',
          content: `Concurrent message ${i}`
        }));
      }

      await Promise.all(promises);
      expect(memory.storage.messages).toHaveLength(5);
    });
  });
});