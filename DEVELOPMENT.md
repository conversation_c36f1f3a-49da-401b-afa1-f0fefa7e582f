# Development Setup Guide

This guide provides detailed instructions for setting up a development environment for the LLM Memory Test framework.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Environment Configuration](#environment-configuration)
- [Development Workflow](#development-workflow)
- [Testing](#testing)
- [Code Quality](#code-quality)
- [Debugging](#debugging)
- [Common Issues](#common-issues)

## Prerequisites

### Required Software

1. **Node.js** (version 18.0.0 or higher)
   ```bash
   # Check your Node.js version
   node --version
   
   # Install Node.js using nvm (recommended)
   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
   nvm install 18
   nvm use 18
   ```

2. **npm** (comes with Node.js)
   ```bash
   # Check npm version
   npm --version
   
   # Update npm to latest version
   npm install -g npm@latest
   ```

3. **Git**
   ```bash
   # Check Git version
   git --version
   
   # Install Git (if not already installed)
   # macOS: brew install git
   # Ubuntu: sudo apt-get install git
   # Windows: Download from https://git-scm.com/
   ```

### Recommended Tools

1. **Visual Studio Code** with extensions:
   - ESLint
   - Prettier
   - Jest
   - GitLens
   - JavaScript (ES6) code snippets

2. **Terminal/Shell**:
   - macOS/Linux: Built-in terminal
   - Windows: Windows Terminal or WSL2

## Installation

### 1. Clone the Repository

```bash
# Clone your fork
git clone https://github.com/your-username/llm-memory-test.git
cd llm-memory-test

# Add upstream remote
git remote add upstream https://github.com/original-repo/llm-memory-test.git
```

### 2. Install Dependencies

```bash
# Install all dependencies
npm install

# Verify installation
npm run verify
```

### 3. Verify Installation

```bash
# Run installation verification
npm run verify

# Run health check
npm run health-check
```

## Environment Configuration

### 1. Create Environment File

```bash
# Copy the example environment file
cp .env.example .env
```

### 2. Configure Environment Variables

Edit the `.env` file with your settings:

```bash
# Memory Configuration
MEMORY_TYPE=simple
CONTEXT_WINDOW=10
SUMMARY_THRESHOLD=20

# Test Configuration
TEST_FACTS_FILE=data/test_facts_simple.json
FACTS_COUNT=5
MESSAGES_BETWEEN_FACTS=3

# Model Configuration
USER_MODEL=openrouter/anthropic/claude-3-haiku
ASSISTANT_MODEL=openrouter/anthropic/claude-3-haiku
EVALUATOR_MODEL=openrouter/anthropic/claude-3-sonnet
SUMMARY_MODEL=openrouter/anthropic/claude-3-haiku
KNOWLEDGE_EXTRACTION_MODEL=openrouter/anthropic/claude-3-haiku

# API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Performance Configuration
ENABLE_PERFORMANCE_TRACKING=true
PERFORMANCE_LOG_INTERVAL=1000
```

### 3. API Key Setup

#### OpenRouter API Key
1. Visit [OpenRouter](https://openrouter.ai/)
2. Create an account and get your API key
3. Add it to your `.env` file

#### OpenAI API Key (optional)
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Create an account and get your API key
3. Add it to your `.env` file

### 4. Test Data Setup

The framework comes with sample test data files:
- `data/test_facts_simple.json` - Simple test facts
- `data/test_facts_customer.json` - Customer service scenario

You can create custom test data files following the same format.

## Development Workflow

### 1. Branch Management

```bash
# Create a new feature branch
git checkout -b feature/your-feature-name

# Keep your branch up to date
git fetch upstream
git rebase upstream/main
```

### 2. Development Commands

```bash
# Start development with file watching
npm run dev

# Run the application
npm start

# Run tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### 3. Code Quality Commands

```bash
# Check code formatting
npm run format:check

# Format code
npm run format

# Run linting
npm run lint

# Fix linting issues
npm run lint:fix

# Run all pre-commit checks
npm run precommit
```

## Testing

### Test Structure

```
src/
├── __tests__/
│   ├── integration/          # Integration tests
│   └── setup.test.js        # Test setup
├── memory/__tests__/         # Memory implementation tests
├── utils/__tests__/          # Utility function tests
└── config/__tests__/         # Configuration tests
```

### Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test -- src/memory/__tests__/simpleMemory.test.js

# Run tests matching pattern
npm test -- --testNamePattern="should create memory"

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

### Writing Tests

Example test structure:

```javascript
const { SimpleMemory } = require('../simpleMemory');

describe('SimpleMemory', () => {
  let memory;

  beforeEach(() => {
    memory = new SimpleMemory({ contextWindow: 5 });
  });

  describe('addMessage', () => {
    it('should add message to conversation history', () => {
      const message = { role: 'user', content: 'Hello' };
      memory.addMessage(message);
      
      const context = memory.getContext();
      expect(context.messages).toContain(message);
    });
  });
});
```

### Test Coverage

Aim for high test coverage:
- Unit tests: Test individual functions and classes
- Integration tests: Test component interactions
- End-to-end tests: Test complete workflows

## Code Quality

### ESLint Configuration

The project uses ESLint with the Standard configuration:

```javascript
// .eslintrc.js
module.exports = {
  extends: ['standard'],
  env: {
    node: true,
    jest: true
  },
  rules: {
    // Custom rules
  }
};
```

### Prettier Configuration

Code formatting is handled by Prettier:

```javascript
// .prettierrc.js
module.exports = {
  semi: true,
  trailingComma: 'es5',
  singleQuote: true,
  printWidth: 80,
  tabWidth: 2
};
```

### JSDoc Documentation

All public methods should have JSDoc comments:

```javascript
/**
 * Creates a new memory instance
 * @param {string} type - Memory type (simple, summary, summary_with_knowledge)
 * @param {Object} config - Configuration object
 * @returns {MemoryInterface} Memory instance
 * @throws {Error} When memory type is not supported
 */
function createMemory(type, config) {
  // Implementation
}
```

## Debugging

### Debug Configuration

For Visual Studio Code, create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Application",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/app.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal"
    },
    {
      "name": "Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/jest",
      "args": ["--runInBand"],
      "console": "integratedTerminal"
    }
  ]
}
```

### Logging

Use the built-in logger for debugging:

```javascript
const { Logger } = require('./src/utils/logger');
const logger = new Logger('debug');

logger.debug('Debug message', { context: 'additional data' });
logger.info('Info message');
logger.warn('Warning message');
logger.error('Error message', error);
```

### Performance Debugging

Enable performance tracking:

```bash
# Set in .env
ENABLE_PERFORMANCE_TRACKING=true
LOG_LEVEL=debug
```

## Common Issues

### 1. Node.js Version Issues

```bash
# Error: Node.js version not supported
# Solution: Update Node.js
nvm install 18
nvm use 18
```

### 2. Dependency Installation Issues

```bash
# Error: npm install fails
# Solution: Clear cache and reinstall
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. API Key Issues

```bash
# Error: API key not found or invalid
# Solution: Check .env file
cat .env | grep API_KEY
```

### 4. Test Failures

```bash
# Error: Tests fail due to missing environment
# Solution: Ensure test environment is set up
cp .env.example .env.test
npm test
```

### 5. Memory Issues

```bash
# Error: Out of memory during tests
# Solution: Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm test
```

### 6. Port Conflicts

```bash
# Error: Port already in use
# Solution: Kill process using port
lsof -ti:3000 | xargs kill -9
```

## Development Best Practices

### 1. Code Organization

- Keep functions small and focused
- Use descriptive variable names
- Follow the existing code structure
- Add comments for complex logic

### 2. Git Workflow

- Make small, focused commits
- Write clear commit messages
- Keep branches up to date
- Test before committing

### 3. Testing

- Write tests for new features
- Maintain high test coverage
- Use descriptive test names
- Mock external dependencies

### 4. Documentation

- Update README for new features
- Add JSDoc comments
- Update configuration documentation
- Include usage examples

## Getting Help

### Resources

- **Documentation**: Check the README and other docs
- **Issues**: Search existing GitHub issues
- **Discussions**: Use GitHub Discussions for questions
- **Code**: Review existing code for patterns

### Debugging Steps

1. Check the logs for error messages
2. Verify environment configuration
3. Run health check: `npm run health-check`
4. Check test output: `npm test`
5. Review recent changes: `git log --oneline -10`

### Reporting Issues

When reporting issues, include:

1. **Environment**: Node.js version, OS, npm version
2. **Steps**: How to reproduce the issue
3. **Expected**: What should happen
4. **Actual**: What actually happens
5. **Logs**: Relevant error messages or logs
6. **Configuration**: Relevant parts of your .env file (without API keys)

## Contributing

Once your development environment is set up:

1. Read [CONTRIBUTING.md](CONTRIBUTING.md)
2. Check the [Code of Conduct](CODE_OF_CONDUCT.md)
3. Look for issues labeled `good first issue`
4. Join discussions and ask questions

Happy coding! 🚀