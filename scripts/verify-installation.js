#!/usr/bin/env node

/**
 * Installation verification script for LLM Memory Test
 * Verifies that the package is properly installed and configured
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class InstallationVerifier {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.success = [];
  }

  /**
   * Main verification process
   */
  async verify() {
    console.log('🔍 Verifying LLM Memory Test installation...\n');

    this.checkNodeVersion();
    this.checkPackageInstallation();
    this.checkDependencies();
    this.checkConfigurationFiles();
    this.checkDataFiles();
    this.checkScripts();
    this.checkEnvironmentSetup();

    this.printResults();
    
    if (this.errors.length > 0) {
      process.exit(1);
    }
  }

  /**
   * Check Node.js version compatibility
   */
  checkNodeVersion() {
    try {
      const nodeVersion = process.version;
      const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
      
      if (majorVersion >= 18) {
        this.success.push(`✅ Node.js version ${nodeVersion} is compatible`);
      } else {
        this.errors.push(`❌ Node.js version ${nodeVersion} is not supported. Requires Node.js 18.0.0 or higher`);
      }
    } catch (error) {
      this.errors.push(`❌ Failed to check Node.js version: ${error.message}`);
    }
  }

  /**
   * Check if package is properly installed
   */
  checkPackageInstallation() {
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      if (fs.existsSync(packagePath)) {
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        this.success.push(`✅ Package ${packageJson.name} v${packageJson.version} found`);
        
        // Check if this is the correct package
        if (packageJson.name === 'llm-memory-test') {
          this.success.push('✅ Correct package identified');
        } else {
          this.warnings.push(`⚠️  Package name is ${packageJson.name}, expected llm-memory-test`);
        }
      } else {
        this.errors.push('❌ package.json not found');
      }
    } catch (error) {
      this.errors.push(`❌ Failed to read package.json: ${error.message}`);
    }
  }

  /**
   * Check if all dependencies are installed
   */
  checkDependencies() {
    try {
      const nodeModulesPath = path.join(process.cwd(), 'node_modules');
      if (fs.existsSync(nodeModulesPath)) {
        this.success.push('✅ node_modules directory found');
        
        // Check key dependencies
        const keyDependencies = ['dotenv', 'uuid', 'ajv', '@langchain/openai'];
        for (const dep of keyDependencies) {
          const depPath = path.join(nodeModulesPath, dep);
          if (fs.existsSync(depPath)) {
            this.success.push(`✅ Dependency ${dep} installed`);
          } else {
            this.errors.push(`❌ Missing dependency: ${dep}`);
          }
        }
      } else {
        this.errors.push('❌ node_modules directory not found. Run "npm install"');
      }
    } catch (error) {
      this.errors.push(`❌ Failed to check dependencies: ${error.message}`);
    }
  }

  /**
   * Check configuration files
   */
  checkConfigurationFiles() {
    const configFiles = [
      { file: '.env.example', required: true, description: 'Environment template' },
      { file: '.env', required: false, description: 'Environment configuration' },
      { file: 'jest.config.js', required: true, description: 'Jest configuration' },
      { file: '.eslintrc.js', required: true, description: 'ESLint configuration' }
    ];

    for (const config of configFiles) {
      const filePath = path.join(process.cwd(), config.file);
      if (fs.existsSync(filePath)) {
        this.success.push(`✅ ${config.description} (${config.file}) found`);
      } else if (config.required) {
        this.errors.push(`❌ Missing required file: ${config.file}`);
      } else {
        this.warnings.push(`⚠️  Optional file not found: ${config.file}`);
      }
    }
  }

  /**
   * Check data files
   */
  checkDataFiles() {
    try {
      const dataDir = path.join(process.cwd(), 'data');
      if (fs.existsSync(dataDir)) {
        this.success.push('✅ Data directory found');
        
        const testFiles = fs.readdirSync(dataDir).filter(file => file.endsWith('.json'));
        if (testFiles.length > 0) {
          this.success.push(`✅ Found ${testFiles.length} test data files`);
          
          // Validate JSON format of test files
          for (const file of testFiles) {
            try {
              const filePath = path.join(dataDir, file);
              const content = fs.readFileSync(filePath, 'utf8');
              JSON.parse(content);
              this.success.push(`✅ Test file ${file} is valid JSON`);
            } catch (error) {
              this.errors.push(`❌ Invalid JSON in test file ${file}: ${error.message}`);
            }
          }
        } else {
          this.warnings.push('⚠️  No test data files found in data directory');
        }
      } else {
        this.warnings.push('⚠️  Data directory not found');
      }
    } catch (error) {
      this.errors.push(`❌ Failed to check data files: ${error.message}`);
    }
  }

  /**
   * Check npm scripts
   */
  checkScripts() {
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      const requiredScripts = ['start', 'test', 'lint'];
      const scripts = packageJson.scripts || {};
      
      for (const script of requiredScripts) {
        if (scripts[script]) {
          this.success.push(`✅ npm script "${script}" available`);
        } else {
          this.errors.push(`❌ Missing npm script: ${script}`);
        }
      }
      
      // Test if scripts can be executed (dry run)
      try {
        execSync('npm run lint --dry-run', { stdio: 'pipe' });
        this.success.push('✅ Lint script is executable');
      } catch (error) {
        this.warnings.push('⚠️  Lint script may have issues');
      }
      
    } catch (error) {
      this.errors.push(`❌ Failed to check npm scripts: ${error.message}`);
    }
  }

  /**
   * Check environment setup
   */
  checkEnvironmentSetup() {
    const envPath = path.join(process.cwd(), '.env');
    
    if (fs.existsSync(envPath)) {
      try {
        const envContent = fs.readFileSync(envPath, 'utf8');
        const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
        
        if (envLines.length > 0) {
          this.success.push(`✅ Environment file configured with ${envLines.length} variables`);
          
          // Check for common required variables
          const requiredVars = ['MEMORY_TYPE', 'TEST_FACTS_FILE'];
          for (const varName of requiredVars) {
            if (envContent.includes(varName)) {
              this.success.push(`✅ Environment variable ${varName} configured`);
            } else {
              this.warnings.push(`⚠️  Environment variable ${varName} not found`);
            }
          }
        } else {
          this.warnings.push('⚠️  Environment file exists but appears empty');
        }
      } catch (error) {
        this.errors.push(`❌ Failed to read .env file: ${error.message}`);
      }
    } else {
      this.warnings.push('⚠️  No .env file found. Copy .env.example to .env and configure');
    }
  }

  /**
   * Print verification results
   */
  printResults() {
    console.log('\n📋 Verification Results:\n');
    
    if (this.success.length > 0) {
      console.log('✅ Success:');
      this.success.forEach(msg => console.log(`  ${msg}`));
      console.log('');
    }
    
    if (this.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      this.warnings.forEach(msg => console.log(`  ${msg}`));
      console.log('');
    }
    
    if (this.errors.length > 0) {
      console.log('❌ Errors:');
      this.errors.forEach(msg => console.log(`  ${msg}`));
      console.log('');
    }
    
    console.log('📊 Summary:');
    console.log(`  ✅ ${this.success.length} checks passed`);
    console.log(`  ⚠️  ${this.warnings.length} warnings`);
    console.log(`  ❌ ${this.errors.length} errors`);
    
    if (this.errors.length === 0) {
      console.log('\n🎉 Installation verification completed successfully!');
      console.log('\nNext steps:');
      console.log('1. Configure your .env file with API keys and settings');
      console.log('2. Run "npm test" to verify everything works');
      console.log('3. Run "npm start" to begin testing memory implementations');
    } else {
      console.log('\n❌ Installation verification failed. Please fix the errors above.');
    }
  }
}

// Run verification if called directly
if (require.main === module) {
  const verifier = new InstallationVerifier();
  verifier.verify().catch(error => {
    console.error('❌ Verification failed:', error.message);
    process.exit(1);
  });
}

module.exports = InstallationVerifier;