#!/usr/bin/env node

/**
 * Health check script for LLM Memory Test
 * Performs runtime health checks and system diagnostics
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

class HealthChecker {
  constructor() {
    this.checks = [];
    this.warnings = [];
    this.errors = [];
  }

  /**
   * Main health check process
   */
  async performHealthCheck() {
    console.log('🏥 Performing LLM Memory Test health check...\n');

    await this.checkSystemResources();
    await this.checkConfiguration();
    await this.checkDependencies();
    await this.checkFilePermissions();
    await this.checkNetworkConnectivity();
    await this.checkMemoryImplementations();
    await this.runBasicFunctionality();

    this.printHealthReport();
    
    if (this.errors.length > 0) {
      process.exit(1);
    }
  }

  /**
   * Check system resources
   */
  async checkSystemResources() {
    try {
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const usedMemory = totalMemory - freeMemory;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;

      this.checks.push({
        category: 'System Resources',
        name: 'Memory Usage',
        status: 'pass',
        details: `${Math.round(memoryUsagePercent)}% used (${Math.round(usedMemory / 1024 / 1024 / 1024)}GB / ${Math.round(totalMemory / 1024 / 1024 / 1024)}GB)`
      });

      if (memoryUsagePercent > 90) {
        this.warnings.push('High memory usage detected. This may affect performance.');
      }

      // Check CPU load
      const loadAvg = os.loadavg();
      const cpuCount = os.cpus().length;
      const loadPercent = (loadAvg[0] / cpuCount) * 100;

      this.checks.push({
        category: 'System Resources',
        name: 'CPU Load',
        status: 'pass',
        details: `${Math.round(loadPercent)}% (${loadAvg[0].toFixed(2)} / ${cpuCount} cores)`
      });

      if (loadPercent > 80) {
        this.warnings.push('High CPU load detected. This may affect performance.');
      }

    } catch (error) {
      this.errors.push(`Failed to check system resources: ${error.message}`);
    }
  }

  /**
   * Check configuration
   */
  async checkConfiguration() {
    try {
      // Load environment configuration
      require('dotenv').config();

      const requiredEnvVars = [
        'MEMORY_TYPE',
        'TEST_FACTS_FILE',
        'USER_MODEL',
        'ASSISTANT_MODEL',
        'EVALUATOR_MODEL'
      ];

      let configuredVars = 0;
      for (const varName of requiredEnvVars) {
        if (process.env[varName]) {
          configuredVars++;
          this.checks.push({
            category: 'Configuration',
            name: `Environment Variable: ${varName}`,
            status: 'pass',
            details: 'Configured'
          });
        } else {
          this.checks.push({
            category: 'Configuration',
            name: `Environment Variable: ${varName}`,
            status: 'fail',
            details: 'Not configured'
          });
          this.errors.push(`Missing required environment variable: ${varName}`);
        }
      }

      // Check memory type validity
      const memoryType = process.env.MEMORY_TYPE;
      const validMemoryTypes = ['simple', 'summary', 'summary_with_knowledge'];
      if (memoryType && validMemoryTypes.includes(memoryType)) {
        this.checks.push({
          category: 'Configuration',
          name: 'Memory Type',
          status: 'pass',
          details: memoryType
        });
      } else if (memoryType) {
        this.checks.push({
          category: 'Configuration',
          name: 'Memory Type',
          status: 'fail',
          details: `Invalid type: ${memoryType}`
        });
        this.errors.push(`Invalid memory type: ${memoryType}. Must be one of: ${validMemoryTypes.join(', ')}`);
      }

    } catch (error) {
      this.errors.push(`Failed to check configuration: ${error.message}`);
    }
  }

  /**
   * Check dependencies
   */
  async checkDependencies() {
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
      let installedCount = 0;
      let totalCount = Object.keys(dependencies).length;

      for (const [depName, version] of Object.entries(dependencies)) {
        try {
          // Handle scoped packages and special cases
          const modulePath = depName.startsWith('@') ? depName : depName;
          require.resolve(modulePath);
          installedCount++;
          this.checks.push({
            category: 'Dependencies',
            name: depName,
            status: 'pass',
            details: `Installed (${version})`
          });
        } catch (error) {
          // Check if it's in node_modules directory as fallback
          const nodeModulesPath = path.join(process.cwd(), 'node_modules', depName);
          if (fs.existsSync(nodeModulesPath)) {
            installedCount++;
            this.checks.push({
              category: 'Dependencies',
              name: depName,
              status: 'pass',
              details: `Installed (${version})`
            });
          } else {
            this.checks.push({
              category: 'Dependencies',
              name: depName,
              status: 'fail',
              details: 'Not installed'
            });
            this.errors.push(`Missing dependency: ${depName}`);
          }
        }
      }

      if (installedCount === totalCount) {
        this.checks.push({
          category: 'Dependencies',
          name: 'Overall Status',
          status: 'pass',
          details: `All ${totalCount} dependencies installed`
        });
      }

    } catch (error) {
      this.errors.push(`Failed to check dependencies: ${error.message}`);
    }
  }

  /**
   * Check file permissions
   */
  async checkFilePermissions() {
    try {
      const criticalPaths = [
        { path: 'src/', type: 'directory', permission: 'read' },
        { path: 'data/', type: 'directory', permission: 'read' },
        { path: 'package.json', type: 'file', permission: 'read' },
        { path: '.env', type: 'file', permission: 'read', optional: true }
      ];

      for (const item of criticalPaths) {
        const fullPath = path.join(process.cwd(), item.path);
        
        try {
          if (fs.existsSync(fullPath)) {
            fs.accessSync(fullPath, fs.constants.R_OK);
            this.checks.push({
              category: 'File Permissions',
              name: item.path,
              status: 'pass',
              details: `${item.type} readable`
            });
          } else if (!item.optional) {
            this.checks.push({
              category: 'File Permissions',
              name: item.path,
              status: 'fail',
              details: `${item.type} not found`
            });
            this.errors.push(`Required ${item.type} not found: ${item.path}`);
          }
        } catch (error) {
          this.checks.push({
            category: 'File Permissions',
            name: item.path,
            status: 'fail',
            details: `Permission denied`
          });
          this.errors.push(`Cannot access ${item.path}: ${error.message}`);
        }
      }

    } catch (error) {
      this.errors.push(`Failed to check file permissions: ${error.message}`);
    }
  }

  /**
   * Check network connectivity (basic)
   */
  async checkNetworkConnectivity() {
    try {
      // Check if we can resolve DNS
      const dns = require('dns').promises;
      
      try {
        await dns.lookup('openrouter.ai');
        this.checks.push({
          category: 'Network',
          name: 'DNS Resolution',
          status: 'pass',
          details: 'Can resolve external domains'
        });
      } catch (error) {
        this.checks.push({
          category: 'Network',
          name: 'DNS Resolution',
          status: 'fail',
          details: 'Cannot resolve external domains'
        });
        this.warnings.push('DNS resolution failed. Check network connectivity.');
      }

    } catch (error) {
      this.warnings.push(`Network connectivity check failed: ${error.message}`);
    }
  }

  /**
   * Check memory implementations
   */
  async checkMemoryImplementations() {
    try {
      const memoryDir = path.join(process.cwd(), 'src', 'memory');
      
      if (fs.existsSync(memoryDir)) {
        const memoryFiles = fs.readdirSync(memoryDir).filter(file => file.endsWith('.js'));
        
        const expectedFiles = [
          'memoryInterface.js',
          'memoryFactory.js',
          'simpleMemory.js',
          'inMemorySummaryMemory.js'
        ];

        for (const file of expectedFiles) {
          if (memoryFiles.includes(file)) {
            try {
              // Try to require the file to check for syntax errors
              require(path.join(memoryDir, file));
              this.checks.push({
                category: 'Memory Implementations',
                name: file,
                status: 'pass',
                details: 'Loadable'
              });
            } catch (error) {
              this.checks.push({
                category: 'Memory Implementations',
                name: file,
                status: 'fail',
                details: `Syntax error: ${error.message}`
              });
              this.errors.push(`Memory implementation ${file} has syntax errors`);
            }
          } else {
            this.checks.push({
              category: 'Memory Implementations',
              name: file,
              status: 'fail',
              details: 'Missing'
            });
            this.errors.push(`Missing memory implementation: ${file}`);
          }
        }
      } else {
        this.errors.push('Memory implementations directory not found');
      }

    } catch (error) {
      this.errors.push(`Failed to check memory implementations: ${error.message}`);
    }
  }

  /**
   * Run basic functionality test
   */
  async runBasicFunctionality() {
    try {
      // Test configuration loading
      const ConfigManager = require('../src/config/configManager');
      const config = new ConfigManager();
      
      this.checks.push({
        category: 'Functionality',
        name: 'Configuration Loading',
        status: 'pass',
        details: 'ConfigManager loads successfully'
      });

      // Test memory factory
      const MemoryFactory = require('../src/memory/memoryFactory');
      const memoryType = process.env.MEMORY_TYPE || 'simple';
      
      try {
        const memory = MemoryFactory.createMemory(memoryType, 'health-check-session', config);
        this.checks.push({
          category: 'Functionality',
          name: 'Memory Creation',
          status: 'pass',
          details: `${memoryType} memory created successfully`
        });
      } catch (error) {
        this.checks.push({
          category: 'Functionality',
          name: 'Memory Creation',
          status: 'fail',
          details: error.message
        });
        this.errors.push(`Failed to create memory: ${error.message}`);
      }

    } catch (error) {
      this.errors.push(`Basic functionality test failed: ${error.message}`);
    }
  }

  /**
   * Print health report
   */
  printHealthReport() {
    console.log('\n📊 Health Check Report:\n');

    // Group checks by category
    const categories = {};
    for (const check of this.checks) {
      if (!categories[check.category]) {
        categories[check.category] = [];
      }
      categories[check.category].push(check);
    }

    // Print each category
    for (const [category, checks] of Object.entries(categories)) {
      console.log(`📁 ${category}:`);
      for (const check of checks) {
        const icon = check.status === 'pass' ? '✅' : '❌';
        console.log(`  ${icon} ${check.name}: ${check.details}`);
      }
      console.log('');
    }

    // Print warnings
    if (this.warnings.length > 0) {
      console.log('⚠️  Warnings:');
      this.warnings.forEach(warning => console.log(`  ⚠️  ${warning}`));
      console.log('');
    }

    // Print errors
    if (this.errors.length > 0) {
      console.log('❌ Errors:');
      this.errors.forEach(error => console.log(`  ❌ ${error}`));
      console.log('');
    }

    // Summary
    const passCount = this.checks.filter(c => c.status === 'pass').length;
    const failCount = this.checks.filter(c => c.status === 'fail').length;
    
    console.log('📋 Summary:');
    console.log(`  ✅ ${passCount} checks passed`);
    console.log(`  ❌ ${failCount} checks failed`);
    console.log(`  ⚠️  ${this.warnings.length} warnings`);

    if (this.errors.length === 0) {
      console.log('\n🎉 Health check completed successfully!');
      console.log('System is ready for LLM memory testing.');
    } else {
      console.log('\n❌ Health check failed. Please address the errors above.');
    }
  }
}

// Run health check if called directly
if (require.main === module) {
  const checker = new HealthChecker();
  checker.performHealthCheck().catch(error => {
    console.error('❌ Health check failed:', error.message);
    process.exit(1);
  });
}

module.exports = HealthChecker;