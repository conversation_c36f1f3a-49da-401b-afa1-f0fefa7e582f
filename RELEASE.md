# Release Process Documentation

This document outlines the release process for the LLM Memory Test framework, including versioning strategy, release preparation, and publication procedures.

## Table of Contents

- [Versioning Strategy](#versioning-strategy)
- [Release Types](#release-types)
- [Pre-Release Checklist](#pre-release-checklist)
- [Release Process](#release-process)
- [Post-Release Tasks](#post-release-tasks)
- [Hotfix Process](#hotfix-process)
- [Automation Scripts](#automation-scripts)

## Versioning Strategy

This project follows [Semantic Versioning (SemVer)](https://semver.org/) with the format `MAJOR.MINOR.PATCH`:

- **MAJOR** (X.0.0): Breaking changes that require user action
- **MINOR** (X.Y.0): New features that are backwards compatible
- **PATCH** (X.Y.Z): Bug fixes that are backwards compatible

### Version Examples

- `1.0.0` → `1.0.1`: Bug fix (patch)
- `1.0.1` → `1.1.0`: New feature (minor)
- `1.1.0` → `2.0.0`: Breaking change (major)

### Pre-Release Versions

For pre-release versions, use the following suffixes:

- `1.1.0-alpha.1`: Alpha release (early development)
- `1.1.0-beta.1`: Beta release (feature complete, testing)
- `1.1.0-rc.1`: Release candidate (final testing)

## Release Types

### Patch Release (1.0.0 → 1.0.1)

**When to use:**
- Bug fixes
- Security patches
- Documentation updates
- Performance improvements (non-breaking)

**Examples:**
- Fix memory leak in conversation simulation
- Correct typos in documentation
- Update dependencies for security

### Minor Release (1.0.0 → 1.1.0)

**When to use:**
- New features
- New memory implementations
- Enhanced functionality
- Deprecation warnings (without removal)

**Examples:**
- Add Redis-based memory implementation
- New evaluation metrics
- Enhanced configuration options
- Additional API endpoints

### Major Release (1.0.0 → 2.0.0)

**When to use:**
- Breaking API changes
- Removed deprecated features
- Major architecture changes
- Minimum Node.js version changes

**Examples:**
- Change memory interface signature
- Remove deprecated configuration options
- Require Node.js 20+ instead of 18+
- Major refactoring of core APIs

## Pre-Release Checklist

### Code Quality

- [ ] All tests pass (`npm test`)
- [ ] Code coverage meets requirements (`npm run test:coverage`)
- [ ] Linting passes (`npm run lint`)
- [ ] Code formatting is correct (`npm run format:check`)
- [ ] No security vulnerabilities (`npm audit`)

### Documentation

- [ ] README.md is up to date
- [ ] CHANGELOG.md includes all changes
- [ ] API documentation is current
- [ ] Configuration documentation is accurate
- [ ] Examples work with new version

### Testing

- [ ] Unit tests cover new functionality
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance regression testing
- [ ] Cross-platform testing (if applicable)

### Dependencies

- [ ] Dependencies are up to date
- [ ] No unused dependencies
- [ ] Security vulnerabilities addressed
- [ ] License compatibility verified

### Compatibility

- [ ] Backwards compatibility maintained (minor/patch)
- [ ] Breaking changes documented (major)
- [ ] Migration guide provided (if needed)
- [ ] Minimum Node.js version verified

## Release Process

### 1. Prepare Release Branch

```bash
# Create release branch from main
git checkout main
git pull origin main
git checkout -b release/v1.1.0

# Update version in package.json
npm version --no-git-tag-version 1.1.0
```

### 2. Update Documentation

```bash
# Update CHANGELOG.md
# Move items from [Unreleased] to [1.1.0] - YYYY-MM-DD
# Add release date and version

# Update README.md if needed
# Update any version references
# Update installation instructions if changed
```

### 3. Run Pre-Release Checks

```bash
# Run full test suite
npm run test:coverage

# Run linting and formatting
npm run lint
npm run format:check

# Run security audit
npm audit

# Run installation verification
npm run verify

# Run health check
npm run health-check
```

### 4. Create Release Commit

```bash
# Commit changes
git add .
git commit -m "chore: prepare release v1.1.0"

# Push release branch
git push origin release/v1.1.0
```

### 5. Create Pull Request

Create a pull request from `release/v1.1.0` to `main`:

- Title: "Release v1.1.0"
- Description: Include changelog entries
- Request review from maintainers
- Ensure all CI checks pass

### 6. Merge and Tag

After PR approval:

```bash
# Merge to main
git checkout main
git pull origin main

# Create and push tag
git tag -a v1.1.0 -m "Release version 1.1.0"
git push origin v1.1.0
```

### 7. Publish to npm

```bash
# Verify package contents
npm pack
tar -tzf llm-memory-test-1.1.0.tgz

# Publish to npm
npm publish

# Verify publication
npm view llm-memory-test@1.1.0
```

### 8. Create GitHub Release

1. Go to GitHub repository
2. Click "Releases" → "Create a new release"
3. Select tag `v1.1.0`
4. Title: "Release v1.1.0"
5. Description: Copy from CHANGELOG.md
6. Attach any relevant files
7. Publish release

## Post-Release Tasks

### 1. Update Main Branch

```bash
# Ensure main is up to date
git checkout main
git pull origin main

# Delete release branch
git branch -d release/v1.1.0
git push origin --delete release/v1.1.0
```

### 2. Update Documentation

- [ ] Update documentation website (if applicable)
- [ ] Update examples and tutorials
- [ ] Notify documentation maintainers

### 3. Communication

- [ ] Announce release on relevant channels
- [ ] Update project status/roadmap
- [ ] Respond to user feedback

### 4. Monitor Release

- [ ] Monitor npm download statistics
- [ ] Watch for bug reports
- [ ] Monitor GitHub issues
- [ ] Check CI/CD status

## Hotfix Process

For critical bugs that need immediate release:

### 1. Create Hotfix Branch

```bash
# Create hotfix branch from latest release tag
git checkout v1.1.0
git checkout -b hotfix/v1.1.1
```

### 2. Apply Fix

```bash
# Make necessary changes
# Add tests for the fix
# Update CHANGELOG.md

# Commit changes
git add .
git commit -m "fix: critical bug description"
```

### 3. Test Hotfix

```bash
# Run focused tests
npm test

# Run full verification
npm run verify
npm run health-check
```

### 4. Release Hotfix

```bash
# Update version
npm version --no-git-tag-version 1.1.1

# Commit version bump
git add package.json
git commit -m "chore: bump version to 1.1.1"

# Merge to main
git checkout main
git merge hotfix/v1.1.1

# Create tag and publish
git tag -a v1.1.1 -m "Hotfix release v1.1.1"
git push origin main v1.1.1

# Publish to npm
npm publish
```

## Automation Scripts

### Release Scripts (package.json)

```json
{
  "scripts": {
    "release:patch": "npm version patch && npm publish",
    "release:minor": "npm version minor && npm publish",
    "release:major": "npm version major && npm publish",
    "preversion": "npm run test && npm run lint",
    "version": "npm run format && git add -A src",
    "postversion": "git push && git push --tags"
  }
}
```

### Usage

```bash
# Automated patch release
npm run release:patch

# Automated minor release
npm run release:minor

# Automated major release
npm run release:major
```

### Custom Release Script

Create `scripts/release.js`:

```javascript
#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

function release(type) {
  console.log(`🚀 Starting ${type} release...`);
  
  // Run pre-release checks
  console.log('📋 Running pre-release checks...');
  execSync('npm run test', { stdio: 'inherit' });
  execSync('npm run lint', { stdio: 'inherit' });
  execSync('npm audit', { stdio: 'inherit' });
  
  // Update version
  console.log('📝 Updating version...');
  execSync(`npm version ${type}`, { stdio: 'inherit' });
  
  // Publish
  console.log('📦 Publishing to npm...');
  execSync('npm publish', { stdio: 'inherit' });
  
  console.log('✅ Release completed successfully!');
}

const type = process.argv[2];
if (!['patch', 'minor', 'major'].includes(type)) {
  console.error('Usage: node scripts/release.js [patch|minor|major]');
  process.exit(1);
}

release(type);
```

### GitHub Actions (Optional)

Create `.github/workflows/release.yml`:

```yaml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          registry-url: 'https://registry.npmjs.org'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Publish to npm
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      
      - name: Create GitHub Release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
```

## Release Schedule

### Regular Releases

- **Patch releases**: As needed for bug fixes
- **Minor releases**: Monthly or bi-monthly
- **Major releases**: Quarterly or as needed

### Security Releases

- **Critical security issues**: Within 24-48 hours
- **Non-critical security issues**: Within 1 week

### LTS (Long Term Support)

- Major versions are supported for 12 months
- Security patches provided for 18 months
- Clear migration paths provided

## Rollback Procedure

If a release needs to be rolled back:

### 1. Unpublish from npm (if possible)

```bash
# Only possible within 24 hours
npm unpublish llm-memory-test@1.1.0
```

### 2. Create Rollback Release

```bash
# Revert problematic changes
git revert <commit-hash>

# Create new patch release
npm version patch
npm publish
```

### 3. Communicate Issue

- Update GitHub release with warning
- Notify users through appropriate channels
- Document the issue and resolution

## Best Practices

### Version Planning

- Plan major releases in advance
- Communicate breaking changes early
- Provide migration guides
- Maintain backwards compatibility when possible

### Quality Assurance

- Never skip testing for releases
- Use automated checks where possible
- Test on multiple environments
- Validate with real-world scenarios

### Communication

- Keep changelog up to date
- Use clear, descriptive commit messages
- Announce releases appropriately
- Respond to user feedback promptly

### Security

- Regularly audit dependencies
- Address security issues promptly
- Follow responsible disclosure practices
- Keep security patches minimal and focused

This release process ensures consistent, high-quality releases while maintaining project stability and user trust.